/*
 * Copyright (C) 2020, Inria
 * GRAPHDECO research group, https://team.inria.fr/graphdeco
 * All rights reserved.
 *
 * This software is free for non-commercial, research and evaluation use 
 * under the terms of the LICENSE.md file.
 *
 * For <NAME_EMAIL> and/or <PERSON><PERSON>@inria.fr
 */


#version 420

layout(binding = 0) uniform sampler2D left;
layout(binding = 1) uniform sampler2D right;
layout(location= 0) out vec4 out_color;

in vec2 vertex_coord;

void main(void) {
    vec2 texcoord = (vertex_coord + vec2(1.0)) / 2.0;
    vec4 cl = texture(left, texcoord);
    vec4 cr = texture(right, texcoord);
    out_color = vec4(cl.r, cr.g, cr.b, 1.0);
}
