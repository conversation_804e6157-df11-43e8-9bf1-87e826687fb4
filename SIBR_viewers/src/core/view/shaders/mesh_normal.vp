/*
 * Copyright (C) 2020, Inria
 * GRAPHDECO research group, https://team.inria.fr/graphdeco
 * All rights reserved.
 *
 * This software is free for non-commercial, research and evaluation use 
 * under the terms of the LICENSE.md file.
 *
 * For <NAME_EMAIL> and/or <PERSON>@inria.fr
 */


#version 420

uniform mat4 proj;

layout(location = 0) in vec3 in_vertex;
//layout(location = 1) in vec2 in_texcoord;
layout(location = 1) in vec3 in_normal;

//out vec2 texture_coord;
out vec3 normal_coord;

void main(void) {
	gl_Position = proj * vec4(in_vertex,1.0);
	//texture_coord = in_texcoord;
  normal_coord  = in_normal;
}
