extlibs/
build/
install/
src/projects/*
cmake-gui.exe.stackdump
__pycache__/

# emacs garbage
\#*
.\#*

# vim garbage
*.swp
*.swo
*.idea/
*.log
*.sh
*.tmp

hs_err_*

# re include common public projects
!src/projects/ulr/
!src/projects/dataset_tools/

# more vim garbage
# Swap
[._]*.s[a-v][a-z]
!*.svg  # comment out if you don't need vector files
[._]*.sw[a-p]
[._]s[a-rt-v][a-z]
[._]ss[a-gi-z]
[._]sw[a-p]

# Session
Session.vim
Sessionx.vim

# Temporary
.netrwhist
*~
# Auto-generated tag files
tags
# Persistent undo
[._]*.un~