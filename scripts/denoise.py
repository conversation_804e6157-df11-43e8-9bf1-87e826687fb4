import os
import numpy as np
from plyfile import PlyData
import torch
import laspy

class GSDenoise:
    def __init__(self, ply_file):
        plydata = PlyData.read(ply_file)

        self.xyz = np.stack((np.asarray(plydata.elements[0]["x"]),
                        np.asarray(plydata.elements[0]["y"]),
                        np.asarray(plydata.elements[0]["z"])),  axis=1)
        self.opacities = torch.sigmoid(torch.from_numpy(np.asarray(plydata.elements[0]["opacity"]))).numpy()
        self.scaling = np.stack((np.asarray(plydata.elements[0]["scale_0"]),
                               np.asarray(plydata.elements[0]["scale_1"]), 
                               np.asarray(plydata.elements[0]["scale_2"])), axis=1)
        self.scaling = np.max(self.scaling, axis=1)

    def prune_points_by_opacity(self, thres_opacity):
        mask = self.opacities > thres_opacity
        # self.xyz = self.xyz[mask]
        # self.opacities = self.opacities[mask]
        # self.scaling = self.scaling[mask]
        # print(f'prune points by opacity, before: {len(mask)}, after: {np.sum(mask)}')
        return mask

    def prune_by_scaling(self, scale=2):
        mean = np.mean(self.scaling)
        std_dev = np.std(self.scaling)

        # lower_bound = mean - 2 * std_dev
        upper_bound = mean + scale * std_dev
        print(f'prune points by scaling {upper_bound}')

        mask = self.scaling <= upper_bound
        # self.xyz = self.xyz[mask]
        # self.opacities = self.opacities[mask]
        # self.scaling = self.scaling[mask]
        # print(f'prune points by scaling {upper_bound}, before: {len(mask)}, after: {np.sum(mask)}')
        return mask
        

    
    def save_las(self, las_file):
        # Create new LAS file
        las = laspy.create(point_format=2, file_version="1.2")

        # Write XYZ coordinates
        las.x = self.xyz[:, 0]
        las.y = self.xyz[:, 1] 
        las.z = self.xyz[:, 2]

        # Store opacity as user-defined extra dimension
        opacity_dim = laspy.ExtraBytesParams(
            name="opacity",
            description="Point opacity value",
            type=np.float32
        )
        las.add_extra_dim(opacity_dim)
        las.opacity = self.opacities.flatten()

        # Store scaling as user-defined extra dimension
        scaling_dim = laspy.ExtraBytesParams(
            name="scaling",
            description="Point scaling value",
            type=np.float32
        )
        las.add_extra_dim(scaling_dim)
        las.scaling = self.scaling.flatten()

        # Write to file
        las.write(las_file)

if __name__ == '__main__':
    ply_file = 'output/truck_depth/point_cloud/iteration_30000/point_cloud.ply'
    save_folder = 'output/truck_depth/denoise'
    gsdenoise = GSDenoise(ply_file)
    mask_opacity = gsdenoise.prune_points_by_opacity(thres_opacity=0.2)
    mask_scaling = gsdenoise.prune_by_scaling(scale=1.5)
    mask = mask_opacity & mask_scaling
    gsdenoise.xyz = gsdenoise.xyz[mask]
    gsdenoise.opacities = gsdenoise.opacities[mask]
    gsdenoise.scaling = gsdenoise.scaling[mask]
    print(f'prune points by opacity and scaling, before: {len(mask_opacity)}, after: {np.sum(mask)}')
    os.makedirs(save_folder, exist_ok=True)
    gsdenoise.save_las(os.path.join(save_folder, 'denoise.las'))
