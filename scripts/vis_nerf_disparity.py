import numpy as np
import cv2
import os
import sys
import re

def verify_depth_consistency(left_img,right_img, disparity):
    height, width = left_img.shape[:2]

    x_coords = np.arange(width)
    y_coords = np.arange(height)
    xx, yy = np.meshgrid(x_coords, y_coords)
    
    disparity[disparity==0] = width*2
    
    # Fill in the mapping coordinates
    map_x = (xx+disparity).astype(np.float32)
    map_y = yy.astype(np.float32)
    
    # if left_img.dtype != np.uint8:
    #     left_img = (left_img * 255).astype(np.uint8)
    #     left_img = left_img[...,::-1]
    # Remap left image to right view perspective
    # dst(x,y) =  src(map_x(x,y),map_y(x,y))

    left_img_warped = cv2.remap(left_img, map_x, map_y, interpolation=cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT)
    
    # Save warped image for visualization
    vis_warped = np.concatenate([right_img, left_img_warped], axis=0)
    return vis_warped
    # cv2.imwrite('test_data/nerf_data/vis_warped.png', vis_warped)

def readpfm(file):
    file = open(file, 'rb')

    color = None
    width = None
    height = None
    scale = None
    endian = None

    header = file.readline().rstrip()
    if (sys.version[0]) == '3':
        header = header.decode('utf-8')
    if header == 'PF':
        color = True
    elif header == 'Pf':
        color = False
    else:
        raise Exception('Not a PFM file.')

    if (sys.version[0]) == '3':
        dim_match = re.match(r'^(\d+)\s(\d+)\s$', file.readline().decode('utf-8'))
    else:
        dim_match = re.match(r'^(\d+)\s(\d+)\s$', file.readline())
    if dim_match:
        width, height = map(int, dim_match.groups())
    else:
        raise Exception('Malformed PFM header.')

    if (sys.version[0]) == '3':
        scale = float(file.readline().rstrip().decode('utf-8'))
    else:
        scale = float(file.readline().rstrip())

    if scale < 0:  # little-endian
        endian = '<'
        scale = -scale
    else:
        endian = '>'  # big-endian

    data = np.fromfile(file, endian + 'f')
    shape = (height, width, 3) if color else (height, width)

    data = np.reshape(data, shape)
    data = np.flipud(data)
    return data, scale

def load_nerf_data(baseline="03",img_name = 'IMG_20220818_180330'):
    
    center_file = f'test_data/nerf_data/left/{img_name}.jpg'
    right_file = f'test_data/nerf_data/{baseline}/right/{img_name}.jpg'
    disparity_file = f'test_data/nerf_data/{baseline}/disp/{img_name}.png'
    confidence_file = f'test_data/nerf_data/AO/{img_name}.png'

    left_img = cv2.imread(center_file)
    right_img = cv2.imread(right_file)
    # Read disparity map as uint16
    disparity = cv2.imread(disparity_file, cv2.IMREAD_UNCHANGED) / 64 
    confidence = cv2.imread(confidence_file, cv2.IMREAD_UNCHANGED) / 65536
    disparity = disparity * (confidence>0.5)
    disparity[np.isnan(disparity)] = 0
    # disparity[disparity>512] = 0
    return left_img, right_img, disparity

def load_mideval_data():
    left_file = '/data/zhaoxin_data/dataset/3D/MiddEval3/trainingQ/Adirondack/im0.png'
    right_file = '/data/zhaoxin_data/dataset/3D/MiddEval3/trainingQ/Adirondack/im1.png'
    disparity_file = '/data/zhaoxin_data/dataset/3D/MiddEval3/trainingQ/Adirondack/disp0GT.pfm'

    left_img = cv2.imread(left_file)
    right_img = cv2.imread(right_file)
    disparity = readpfm(disparity_file)[0]
    disparity[disparity == np.float32(np.inf)] = 0
    # disparity[np.isnan(disparity)] = 0
    return left_img, right_img, disparity

def load_depthSplat_depth(depth_file, baseline, f, left_img_f, right_img_f):
    depth = np.load(depth_file)
    disp = baseline * f / depth
    disp_h, disp_w = disp.shape[:2]
    left_img = cv2.imread(left_img_f)
    right_img = cv2.imread(right_img_f)
    h,w = left_img.shape[:2]
    disp = cv2.resize(disp, (w,h))
    return left_img, right_img, disp

img_name = 'DJI_1'
baseline = 'depthSplat'
# left_img, right_img, disparity = load_nerf_data(baseline,img_name)
# left_img, right_img, disparity = load_mideval_data()

save_folder = f'output/DJI_1/{baseline}'
os.makedirs(save_folder, exist_ok=True)

f = 3754.630890316741
depth_file = '/workspace/3D/depthsplat/DJI_1_depth.npy'
left_img_f = 'output/DJI_1/customviews_baseline0.3/00000_left.jpg'
right_img_f = 'output/DJI_1/customviews_baseline0.3/00000_right.jpg'
left_img, right_img, disparity = load_depthSplat_depth(depth_file, 0.3, f, left_img_f, right_img_f)

disparity_min = disparity.min()
disparity_max = disparity.max()
disparity_norm = (disparity - disparity_min) / (disparity_max - disparity_min)
disparity_vis = (disparity_norm * 255).astype('uint8')
disparity_color = cv2.applyColorMap(disparity_vis, cv2.COLORMAP_JET)
left_disp_vis = np.concatenate((left_img, disparity_color), axis=0)

cv2.imwrite(os.path.join(save_folder, f'{img_name}_left_disp_vis.png'), left_disp_vis)

vis_warped = verify_depth_consistency(left_img, right_img, disparity)
cv2.imwrite(os.path.join(save_folder, f'{img_name}_vis_warped.png'), vis_warped)





   

