<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_integer</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_integer<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00042.html" title="GLM_GTX_integer ">glm/gtx/integer.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:gada7e83fdfe943aba4f1d5bf80cb66f40"><td class="memItemLeft" align="right" valign="top">typedef signed int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00330.html#gada7e83fdfe943aba4f1d5bf80cb66f40">sint</a></td></tr>
<tr class="memdesc:gada7e83fdfe943aba4f1d5bf80cb66f40"><td class="mdescLeft">&#160;</td><td class="mdescRight">32bit signed integer.  <a href="a00330.html#gada7e83fdfe943aba4f1d5bf80cb66f40">More...</a><br /></td></tr>
<tr class="separator:gada7e83fdfe943aba4f1d5bf80cb66f40"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga8cbd3120905f398ec321b5d1836e08fb"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga8cbd3120905f398ec321b5d1836e08fb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00330.html#ga8cbd3120905f398ec321b5d1836e08fb">factorial</a> (genType const &amp;x)</td></tr>
<tr class="memdesc:ga8cbd3120905f398ec321b5d1836e08fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the factorial value of a number (!12 max, integer only) From GLM_GTX_integer extension.  <a href="a00330.html#ga8cbd3120905f398ec321b5d1836e08fb">More...</a><br /></td></tr>
<tr class="separator:ga8cbd3120905f398ec321b5d1836e08fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7011b4e1c1e1ed492149b028feacc00e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL unsigned int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00330.html#ga7011b4e1c1e1ed492149b028feacc00e">floor_log2</a> (unsigned int x)</td></tr>
<tr class="memdesc:ga7011b4e1c1e1ed492149b028feacc00e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the floor log2 of x.  <a href="a00330.html#ga7011b4e1c1e1ed492149b028feacc00e">More...</a><br /></td></tr>
<tr class="separator:ga7011b4e1c1e1ed492149b028feacc00e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaabfbb41531ab7ad8d06fc176edfba785"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00330.html#gaabfbb41531ab7ad8d06fc176edfba785">mod</a> (int x, int y)</td></tr>
<tr class="memdesc:gaabfbb41531ab7ad8d06fc176edfba785"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modulus.  <a href="a00330.html#gaabfbb41531ab7ad8d06fc176edfba785">More...</a><br /></td></tr>
<tr class="separator:gaabfbb41531ab7ad8d06fc176edfba785"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga63fc8d63e7da1706439233b386ba8b6f"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00330.html#ga63fc8d63e7da1706439233b386ba8b6f">mod</a> (uint x, uint y)</td></tr>
<tr class="memdesc:ga63fc8d63e7da1706439233b386ba8b6f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modulus.  <a href="a00330.html#ga63fc8d63e7da1706439233b386ba8b6f">More...</a><br /></td></tr>
<tr class="separator:ga63fc8d63e7da1706439233b386ba8b6f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga78dff8bdb361bf0061194c93e003d189"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00330.html#ga78dff8bdb361bf0061194c93e003d189">nlz</a> (uint x)</td></tr>
<tr class="memdesc:ga78dff8bdb361bf0061194c93e003d189"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the number of leading zeros.  <a href="a00330.html#ga78dff8bdb361bf0061194c93e003d189">More...</a><br /></td></tr>
<tr class="separator:ga78dff8bdb361bf0061194c93e003d189"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga465016030a81d513fa2fac881ebdaa83"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00330.html#ga465016030a81d513fa2fac881ebdaa83">pow</a> (int x, uint y)</td></tr>
<tr class="memdesc:ga465016030a81d513fa2fac881ebdaa83"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns x raised to the y power.  <a href="a00330.html#ga465016030a81d513fa2fac881ebdaa83">More...</a><br /></td></tr>
<tr class="separator:ga465016030a81d513fa2fac881ebdaa83"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga998e5ee915d3769255519e2fbaa2bbf0"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00330.html#ga998e5ee915d3769255519e2fbaa2bbf0">pow</a> (uint x, uint y)</td></tr>
<tr class="memdesc:ga998e5ee915d3769255519e2fbaa2bbf0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns x raised to the y power.  <a href="a00330.html#ga998e5ee915d3769255519e2fbaa2bbf0">More...</a><br /></td></tr>
<tr class="separator:ga998e5ee915d3769255519e2fbaa2bbf0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7ce36693a75879ccd9bb10167cfa722d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00330.html#ga7ce36693a75879ccd9bb10167cfa722d">sqrt</a> (int x)</td></tr>
<tr class="memdesc:ga7ce36693a75879ccd9bb10167cfa722d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the positive square root of x.  <a href="a00330.html#ga7ce36693a75879ccd9bb10167cfa722d">More...</a><br /></td></tr>
<tr class="separator:ga7ce36693a75879ccd9bb10167cfa722d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1975d318978d6dacf78b6444fa5ed7bc"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00330.html#ga1975d318978d6dacf78b6444fa5ed7bc">sqrt</a> (uint x)</td></tr>
<tr class="memdesc:ga1975d318978d6dacf78b6444fa5ed7bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the positive square root of x.  <a href="a00330.html#ga1975d318978d6dacf78b6444fa5ed7bc">More...</a><br /></td></tr>
<tr class="separator:ga1975d318978d6dacf78b6444fa5ed7bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00042.html" title="GLM_GTX_integer ">glm/gtx/integer.hpp</a>&gt; to use the features of this extension. </p>
<p>Add support for integer for core functions </p>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="gada7e83fdfe943aba4f1d5bf80cb66f40"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef signed int sint</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>32bit signed integer. </p>
<p>From GLM_GTX_integer extension. </p>

<p>Definition at line <a class="el" href="a00042_source.html#l00055">55</a> of file <a class="el" href="a00042_source.html">gtx/integer.hpp</a>.</p>

</div>
</div>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga8cbd3120905f398ec321b5d1836e08fb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::factorial </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the factorial value of a number (!12 max, integer only) From GLM_GTX_integer extension. </p>

</div>
</div>
<a class="anchor" id="ga7011b4e1c1e1ed492149b028feacc00e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL unsigned int glm::floor_log2 </td>
          <td>(</td>
          <td class="paramtype">unsigned int&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the floor log2 of x. </p>
<p>From GLM_GTX_integer extension. </p>

</div>
</div>
<a class="anchor" id="gaabfbb41531ab7ad8d06fc176edfba785"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int glm::mod </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modulus. </p>
<p>Returns x - y * floor(x / y) for each component in x using the floating point value y. From GLM_GTX_integer extension. </p>

</div>
</div>
<a class="anchor" id="ga63fc8d63e7da1706439233b386ba8b6f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint glm::mod </td>
          <td>(</td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modulus. </p>
<p>Returns x - y * floor(x / y) for each component in x using the floating point value y. From GLM_GTX_integer extension. </p>

</div>
</div>
<a class="anchor" id="ga78dff8bdb361bf0061194c93e003d189"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint glm::nlz </td>
          <td>(</td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the number of leading zeros. </p>
<p>From GLM_GTX_integer extension. </p>

</div>
</div>
<a class="anchor" id="ga465016030a81d513fa2fac881ebdaa83"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int glm::pow </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns x raised to the y power. </p>
<p>From GLM_GTX_integer extension. </p>

</div>
</div>
<a class="anchor" id="ga998e5ee915d3769255519e2fbaa2bbf0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint glm::pow </td>
          <td>(</td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns x raised to the y power. </p>
<p>From GLM_GTX_integer extension. </p>

</div>
</div>
<a class="anchor" id="ga7ce36693a75879ccd9bb10167cfa722d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int glm::sqrt </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the positive square root of x. </p>
<p>From GLM_GTX_integer extension. </p>

</div>
</div>
<a class="anchor" id="ga1975d318978d6dacf78b6444fa5ed7bc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint glm::sqrt </td>
          <td>(</td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the positive square root of x. </p>
<p>From GLM_GTX_integer extension. </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
