<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Vector types with precision qualifiers</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">Vector types with precision qualifiers<div class="ingroups"><a class="el" href="a00280.html">Core features</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Vector types with precision qualifiers which may result in various precision in term of ULPs.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:gac6c781a85f012d77a75310a3058702c2"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gac6c781a85f012d77a75310a3058702c2">highp_bvec2</a></td></tr>
<tr class="memdesc:gac6c781a85f012d77a75310a3058702c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of high qualifier bool numbers.  <a href="a00282.html#gac6c781a85f012d77a75310a3058702c2">More...</a><br /></td></tr>
<tr class="separator:gac6c781a85f012d77a75310a3058702c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaedb70027d89a0a405046aefda4eabaa6"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gaedb70027d89a0a405046aefda4eabaa6">highp_bvec3</a></td></tr>
<tr class="memdesc:gaedb70027d89a0a405046aefda4eabaa6"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of high qualifier bool numbers.  <a href="a00282.html#gaedb70027d89a0a405046aefda4eabaa6">More...</a><br /></td></tr>
<tr class="separator:gaedb70027d89a0a405046aefda4eabaa6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaee663ff64429443ab07a5327074192f6"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gaee663ff64429443ab07a5327074192f6">highp_bvec4</a></td></tr>
<tr class="memdesc:gaee663ff64429443ab07a5327074192f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of high qualifier bool numbers.  <a href="a00282.html#gaee663ff64429443ab07a5327074192f6">More...</a><br /></td></tr>
<tr class="separator:gaee663ff64429443ab07a5327074192f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab98d77cca255914f5e29697fcbc2d975"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gab98d77cca255914f5e29697fcbc2d975">highp_dvec2</a></td></tr>
<tr class="memdesc:gab98d77cca255914f5e29697fcbc2d975"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of high double-qualifier floating-point numbers.  <a href="a00282.html#gab98d77cca255914f5e29697fcbc2d975">More...</a><br /></td></tr>
<tr class="separator:gab98d77cca255914f5e29697fcbc2d975"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab24dc20dcdc5b71282634bdbf6b70105"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gab24dc20dcdc5b71282634bdbf6b70105">highp_dvec3</a></td></tr>
<tr class="memdesc:gab24dc20dcdc5b71282634bdbf6b70105"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of high double-qualifier floating-point numbers.  <a href="a00282.html#gab24dc20dcdc5b71282634bdbf6b70105">More...</a><br /></td></tr>
<tr class="separator:gab24dc20dcdc5b71282634bdbf6b70105"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab654f4ed4a99d64a6cfc65320c2a7590"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gab654f4ed4a99d64a6cfc65320c2a7590">highp_dvec4</a></td></tr>
<tr class="memdesc:gab654f4ed4a99d64a6cfc65320c2a7590"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of high double-qualifier floating-point numbers.  <a href="a00282.html#gab654f4ed4a99d64a6cfc65320c2a7590">More...</a><br /></td></tr>
<tr class="separator:gab654f4ed4a99d64a6cfc65320c2a7590"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa18f6b80b41c214f10666948539c1f93"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gaa18f6b80b41c214f10666948539c1f93">highp_ivec2</a></td></tr>
<tr class="memdesc:gaa18f6b80b41c214f10666948539c1f93"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of high qualifier signed integer numbers.  <a href="a00282.html#gaa18f6b80b41c214f10666948539c1f93">More...</a><br /></td></tr>
<tr class="separator:gaa18f6b80b41c214f10666948539c1f93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7dd782c3ef5719bc6d5c3ca826b8ad18"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga7dd782c3ef5719bc6d5c3ca826b8ad18">highp_ivec3</a></td></tr>
<tr class="memdesc:ga7dd782c3ef5719bc6d5c3ca826b8ad18"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of high qualifier signed integer numbers.  <a href="a00282.html#ga7dd782c3ef5719bc6d5c3ca826b8ad18">More...</a><br /></td></tr>
<tr class="separator:ga7dd782c3ef5719bc6d5c3ca826b8ad18"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafb84dccdf5d82443df3ffc8428dcaf3e"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gafb84dccdf5d82443df3ffc8428dcaf3e">highp_ivec4</a></td></tr>
<tr class="memdesc:gafb84dccdf5d82443df3ffc8428dcaf3e"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of high qualifier signed integer numbers.  <a href="a00282.html#gafb84dccdf5d82443df3ffc8428dcaf3e">More...</a><br /></td></tr>
<tr class="separator:gafb84dccdf5d82443df3ffc8428dcaf3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad5dd50da9e37387ca6b4e6f9c80fe6f8"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, unsigned int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gad5dd50da9e37387ca6b4e6f9c80fe6f8">highp_uvec2</a></td></tr>
<tr class="memdesc:gad5dd50da9e37387ca6b4e6f9c80fe6f8"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of high qualifier unsigned integer numbers.  <a href="a00282.html#gad5dd50da9e37387ca6b4e6f9c80fe6f8">More...</a><br /></td></tr>
<tr class="separator:gad5dd50da9e37387ca6b4e6f9c80fe6f8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaef61508dd40ec523416697982f9ceaae"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, unsigned int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gaef61508dd40ec523416697982f9ceaae">highp_uvec3</a></td></tr>
<tr class="memdesc:gaef61508dd40ec523416697982f9ceaae"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of high qualifier unsigned integer numbers.  <a href="a00282.html#gaef61508dd40ec523416697982f9ceaae">More...</a><br /></td></tr>
<tr class="separator:gaef61508dd40ec523416697982f9ceaae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaeebd7dd9f3e678691f8620241e5f9221"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, unsigned int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gaeebd7dd9f3e678691f8620241e5f9221">highp_uvec4</a></td></tr>
<tr class="memdesc:gaeebd7dd9f3e678691f8620241e5f9221"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of high qualifier unsigned integer numbers.  <a href="a00282.html#gaeebd7dd9f3e678691f8620241e5f9221">More...</a><br /></td></tr>
<tr class="separator:gaeebd7dd9f3e678691f8620241e5f9221"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa92c1954d71b1e7914874bd787b43d1c"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gaa92c1954d71b1e7914874bd787b43d1c">highp_vec2</a></td></tr>
<tr class="memdesc:gaa92c1954d71b1e7914874bd787b43d1c"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of high single-qualifier floating-point numbers.  <a href="a00282.html#gaa92c1954d71b1e7914874bd787b43d1c">More...</a><br /></td></tr>
<tr class="separator:gaa92c1954d71b1e7914874bd787b43d1c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaca61dfaccbf2f58f2d8063a4e76b44a9"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gaca61dfaccbf2f58f2d8063a4e76b44a9">highp_vec3</a></td></tr>
<tr class="memdesc:gaca61dfaccbf2f58f2d8063a4e76b44a9"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of high single-qualifier floating-point numbers.  <a href="a00282.html#gaca61dfaccbf2f58f2d8063a4e76b44a9">More...</a><br /></td></tr>
<tr class="separator:gaca61dfaccbf2f58f2d8063a4e76b44a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad281decae52948b82feb3a9db8f63a7b"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gad281decae52948b82feb3a9db8f63a7b">highp_vec4</a></td></tr>
<tr class="memdesc:gad281decae52948b82feb3a9db8f63a7b"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of high single-qualifier floating-point numbers.  <a href="a00282.html#gad281decae52948b82feb3a9db8f63a7b">More...</a><br /></td></tr>
<tr class="separator:gad281decae52948b82feb3a9db8f63a7b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5a5452140650988b94d5716e4d872465"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, bool, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga5a5452140650988b94d5716e4d872465">lowp_bvec2</a></td></tr>
<tr class="memdesc:ga5a5452140650988b94d5716e4d872465"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of low qualifier bool numbers.  <a href="a00282.html#ga5a5452140650988b94d5716e4d872465">More...</a><br /></td></tr>
<tr class="separator:ga5a5452140650988b94d5716e4d872465"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga79e0922a977662a8fd39d7829be3908b"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, bool, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga79e0922a977662a8fd39d7829be3908b">lowp_bvec3</a></td></tr>
<tr class="memdesc:ga79e0922a977662a8fd39d7829be3908b"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of low qualifier bool numbers.  <a href="a00282.html#ga79e0922a977662a8fd39d7829be3908b">More...</a><br /></td></tr>
<tr class="separator:ga79e0922a977662a8fd39d7829be3908b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga15ac87724048ab7169bb5d3572939dd3"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, bool, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga15ac87724048ab7169bb5d3572939dd3">lowp_bvec4</a></td></tr>
<tr class="memdesc:ga15ac87724048ab7169bb5d3572939dd3"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of low qualifier bool numbers.  <a href="a00282.html#ga15ac87724048ab7169bb5d3572939dd3">More...</a><br /></td></tr>
<tr class="separator:ga15ac87724048ab7169bb5d3572939dd3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga108086730d086b7f6f7a033955dfb9c3"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga108086730d086b7f6f7a033955dfb9c3">lowp_dvec2</a></td></tr>
<tr class="memdesc:ga108086730d086b7f6f7a033955dfb9c3"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of low double-qualifier floating-point numbers.  <a href="a00282.html#ga108086730d086b7f6f7a033955dfb9c3">More...</a><br /></td></tr>
<tr class="separator:ga108086730d086b7f6f7a033955dfb9c3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga42c518b2917e19ce6946a84c64a3a4b2"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga42c518b2917e19ce6946a84c64a3a4b2">lowp_dvec3</a></td></tr>
<tr class="memdesc:ga42c518b2917e19ce6946a84c64a3a4b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of low double-qualifier floating-point numbers.  <a href="a00282.html#ga42c518b2917e19ce6946a84c64a3a4b2">More...</a><br /></td></tr>
<tr class="separator:ga42c518b2917e19ce6946a84c64a3a4b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0b4432cb8d910e406576d10d802e190d"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, double, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga0b4432cb8d910e406576d10d802e190d">lowp_dvec4</a></td></tr>
<tr class="memdesc:ga0b4432cb8d910e406576d10d802e190d"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of low double-qualifier floating-point numbers.  <a href="a00282.html#ga0b4432cb8d910e406576d10d802e190d">More...</a><br /></td></tr>
<tr class="separator:ga0b4432cb8d910e406576d10d802e190d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8433c6c1fdd80c0a83941d94aff73fa0"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga8433c6c1fdd80c0a83941d94aff73fa0">lowp_ivec2</a></td></tr>
<tr class="memdesc:ga8433c6c1fdd80c0a83941d94aff73fa0"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of low qualifier signed integer numbers.  <a href="a00282.html#ga8433c6c1fdd80c0a83941d94aff73fa0">More...</a><br /></td></tr>
<tr class="separator:ga8433c6c1fdd80c0a83941d94aff73fa0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac1a86a75b3c68ebb704d7094043669d6"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gac1a86a75b3c68ebb704d7094043669d6">lowp_ivec3</a></td></tr>
<tr class="memdesc:gac1a86a75b3c68ebb704d7094043669d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of low qualifier signed integer numbers.  <a href="a00282.html#gac1a86a75b3c68ebb704d7094043669d6">More...</a><br /></td></tr>
<tr class="separator:gac1a86a75b3c68ebb704d7094043669d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga27fc23da61859cd6356326c5f1c796de"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga27fc23da61859cd6356326c5f1c796de">lowp_ivec4</a></td></tr>
<tr class="memdesc:ga27fc23da61859cd6356326c5f1c796de"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of low qualifier signed integer numbers.  <a href="a00282.html#ga27fc23da61859cd6356326c5f1c796de">More...</a><br /></td></tr>
<tr class="separator:ga27fc23da61859cd6356326c5f1c796de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga752ee45136011301b64afd8c310c47a4"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, unsigned int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga752ee45136011301b64afd8c310c47a4">lowp_uvec2</a></td></tr>
<tr class="memdesc:ga752ee45136011301b64afd8c310c47a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of low qualifier unsigned integer numbers.  <a href="a00282.html#ga752ee45136011301b64afd8c310c47a4">More...</a><br /></td></tr>
<tr class="separator:ga752ee45136011301b64afd8c310c47a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b2efbdd6bdc2f8250c57f3e5dc9a292"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, unsigned int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga7b2efbdd6bdc2f8250c57f3e5dc9a292">lowp_uvec3</a></td></tr>
<tr class="memdesc:ga7b2efbdd6bdc2f8250c57f3e5dc9a292"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of low qualifier unsigned integer numbers.  <a href="a00282.html#ga7b2efbdd6bdc2f8250c57f3e5dc9a292">More...</a><br /></td></tr>
<tr class="separator:ga7b2efbdd6bdc2f8250c57f3e5dc9a292"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5e6a632ec1165cf9f54ceeaa5e9b2b1e"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, unsigned int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga5e6a632ec1165cf9f54ceeaa5e9b2b1e">lowp_uvec4</a></td></tr>
<tr class="memdesc:ga5e6a632ec1165cf9f54ceeaa5e9b2b1e"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of low qualifier unsigned integer numbers.  <a href="a00282.html#ga5e6a632ec1165cf9f54ceeaa5e9b2b1e">More...</a><br /></td></tr>
<tr class="separator:ga5e6a632ec1165cf9f54ceeaa5e9b2b1e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga30e8baef5d56d5c166872a2bc00f36e9"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga30e8baef5d56d5c166872a2bc00f36e9">lowp_vec2</a></td></tr>
<tr class="memdesc:ga30e8baef5d56d5c166872a2bc00f36e9"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of low single-qualifier floating-point numbers.  <a href="a00282.html#ga30e8baef5d56d5c166872a2bc00f36e9">More...</a><br /></td></tr>
<tr class="separator:ga30e8baef5d56d5c166872a2bc00f36e9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga868e8e4470a3ef97c7ee3032bf90dc79"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga868e8e4470a3ef97c7ee3032bf90dc79">lowp_vec3</a></td></tr>
<tr class="memdesc:ga868e8e4470a3ef97c7ee3032bf90dc79"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of low single-qualifier floating-point numbers.  <a href="a00282.html#ga868e8e4470a3ef97c7ee3032bf90dc79">More...</a><br /></td></tr>
<tr class="separator:ga868e8e4470a3ef97c7ee3032bf90dc79"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace3acb313c800552a9411953eb8b2ed7"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, float, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gace3acb313c800552a9411953eb8b2ed7">lowp_vec4</a></td></tr>
<tr class="memdesc:gace3acb313c800552a9411953eb8b2ed7"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of low single-qualifier floating-point numbers.  <a href="a00282.html#gace3acb313c800552a9411953eb8b2ed7">More...</a><br /></td></tr>
<tr class="separator:gace3acb313c800552a9411953eb8b2ed7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1e743764869efa9223c2bcefccedaddc"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, bool, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga1e743764869efa9223c2bcefccedaddc">mediump_bvec2</a></td></tr>
<tr class="memdesc:ga1e743764869efa9223c2bcefccedaddc"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of medium qualifier bool numbers.  <a href="a00282.html#ga1e743764869efa9223c2bcefccedaddc">More...</a><br /></td></tr>
<tr class="separator:ga1e743764869efa9223c2bcefccedaddc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga50c783c25082882ef00fe2e5cddba4aa"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, bool, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga50c783c25082882ef00fe2e5cddba4aa">mediump_bvec3</a></td></tr>
<tr class="memdesc:ga50c783c25082882ef00fe2e5cddba4aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of medium qualifier bool numbers.  <a href="a00282.html#ga50c783c25082882ef00fe2e5cddba4aa">More...</a><br /></td></tr>
<tr class="separator:ga50c783c25082882ef00fe2e5cddba4aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0be2c682258604a35004f088782a9645"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, bool, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga0be2c682258604a35004f088782a9645">mediump_bvec4</a></td></tr>
<tr class="memdesc:ga0be2c682258604a35004f088782a9645"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of medium qualifier bool numbers.  <a href="a00282.html#ga0be2c682258604a35004f088782a9645">More...</a><br /></td></tr>
<tr class="separator:ga0be2c682258604a35004f088782a9645"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f4f6e9a69a0281d06940fd0990cafc3"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga2f4f6e9a69a0281d06940fd0990cafc3">mediump_dvec2</a></td></tr>
<tr class="memdesc:ga2f4f6e9a69a0281d06940fd0990cafc3"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of medium double-qualifier floating-point numbers.  <a href="a00282.html#ga2f4f6e9a69a0281d06940fd0990cafc3">More...</a><br /></td></tr>
<tr class="separator:ga2f4f6e9a69a0281d06940fd0990cafc3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga61c3b1dff4ec7c878af80503141b9f37"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga61c3b1dff4ec7c878af80503141b9f37">mediump_dvec3</a></td></tr>
<tr class="memdesc:ga61c3b1dff4ec7c878af80503141b9f37"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of medium double-qualifier floating-point numbers.  <a href="a00282.html#ga61c3b1dff4ec7c878af80503141b9f37">More...</a><br /></td></tr>
<tr class="separator:ga61c3b1dff4ec7c878af80503141b9f37"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga23a8bca00914a51542bfea13a4778186"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, double, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga23a8bca00914a51542bfea13a4778186">mediump_dvec4</a></td></tr>
<tr class="memdesc:ga23a8bca00914a51542bfea13a4778186"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of medium double-qualifier floating-point numbers.  <a href="a00282.html#ga23a8bca00914a51542bfea13a4778186">More...</a><br /></td></tr>
<tr class="separator:ga23a8bca00914a51542bfea13a4778186"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac57496299d276ed97044074097bd5e2c"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gac57496299d276ed97044074097bd5e2c">mediump_ivec2</a></td></tr>
<tr class="memdesc:gac57496299d276ed97044074097bd5e2c"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of medium qualifier signed integer numbers.  <a href="a00282.html#gac57496299d276ed97044074097bd5e2c">More...</a><br /></td></tr>
<tr class="separator:gac57496299d276ed97044074097bd5e2c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga27cfb51e0dbe15bba27a14a8590e8466"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga27cfb51e0dbe15bba27a14a8590e8466">mediump_ivec3</a></td></tr>
<tr class="memdesc:ga27cfb51e0dbe15bba27a14a8590e8466"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of medium qualifier signed integer numbers.  <a href="a00282.html#ga27cfb51e0dbe15bba27a14a8590e8466">More...</a><br /></td></tr>
<tr class="separator:ga27cfb51e0dbe15bba27a14a8590e8466"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga92a204c37e66ac6c1dc7ae91142f2ea5"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga92a204c37e66ac6c1dc7ae91142f2ea5">mediump_ivec4</a></td></tr>
<tr class="memdesc:ga92a204c37e66ac6c1dc7ae91142f2ea5"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of medium qualifier signed integer numbers.  <a href="a00282.html#ga92a204c37e66ac6c1dc7ae91142f2ea5">More...</a><br /></td></tr>
<tr class="separator:ga92a204c37e66ac6c1dc7ae91142f2ea5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa3b4f7806dad03d83bb3da0baa1e3b9b"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, unsigned int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gaa3b4f7806dad03d83bb3da0baa1e3b9b">mediump_uvec2</a></td></tr>
<tr class="memdesc:gaa3b4f7806dad03d83bb3da0baa1e3b9b"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of medium qualifier unsigned integer numbers.  <a href="a00282.html#gaa3b4f7806dad03d83bb3da0baa1e3b9b">More...</a><br /></td></tr>
<tr class="separator:gaa3b4f7806dad03d83bb3da0baa1e3b9b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga83b7df38feefbb357f3673d950fafef7"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, unsigned int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga83b7df38feefbb357f3673d950fafef7">mediump_uvec3</a></td></tr>
<tr class="memdesc:ga83b7df38feefbb357f3673d950fafef7"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of medium qualifier unsigned integer numbers.  <a href="a00282.html#ga83b7df38feefbb357f3673d950fafef7">More...</a><br /></td></tr>
<tr class="separator:ga83b7df38feefbb357f3673d950fafef7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga64ed0deb6573375b7016daf82ffd53a7"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, unsigned int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga64ed0deb6573375b7016daf82ffd53a7">mediump_uvec4</a></td></tr>
<tr class="memdesc:ga64ed0deb6573375b7016daf82ffd53a7"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of medium qualifier unsigned integer numbers.  <a href="a00282.html#ga64ed0deb6573375b7016daf82ffd53a7">More...</a><br /></td></tr>
<tr class="separator:ga64ed0deb6573375b7016daf82ffd53a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabc61976261c406520c7a8e4d946dc3f0"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#gabc61976261c406520c7a8e4d946dc3f0">mediump_vec2</a></td></tr>
<tr class="memdesc:gabc61976261c406520c7a8e4d946dc3f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 components vector of medium single-qualifier floating-point numbers.  <a href="a00282.html#gabc61976261c406520c7a8e4d946dc3f0">More...</a><br /></td></tr>
<tr class="separator:gabc61976261c406520c7a8e4d946dc3f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2384e263df19f1404b733016eff78fca"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga2384e263df19f1404b733016eff78fca">mediump_vec3</a></td></tr>
<tr class="memdesc:ga2384e263df19f1404b733016eff78fca"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 components vector of medium single-qualifier floating-point numbers.  <a href="a00282.html#ga2384e263df19f1404b733016eff78fca">More...</a><br /></td></tr>
<tr class="separator:ga2384e263df19f1404b733016eff78fca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c6978d3ffba06738416a33083853fc0"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, float, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00282.html#ga5c6978d3ffba06738416a33083853fc0">mediump_vec4</a></td></tr>
<tr class="memdesc:ga5c6978d3ffba06738416a33083853fc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 components vector of medium single-qualifier floating-point numbers.  <a href="a00282.html#ga5c6978d3ffba06738416a33083853fc0">More...</a><br /></td></tr>
<tr class="separator:ga5c6978d3ffba06738416a33083853fc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Vector types with precision qualifiers which may result in various precision in term of ULPs. </p>
<p>GLSL allows defining qualifiers for particular variables. With OpenGL's GLSL, these qualifiers have no effect; they are there for compatibility, with OpenGL ES's GLSL, these qualifiers do have an effect.</p>
<p>C++ has no language equivalent to qualifier qualifiers. So GLM provides the next-best thing: a number of typedefs that use a particular qualifier.</p>
<p>None of these types make any guarantees about the actual qualifier used. </p>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="gac6c781a85f012d77a75310a3058702c2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, bool, highp &gt; highp_bvec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of high qualifier bool numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00192_source.html#l00016">16</a> of file <a class="el" href="a00192_source.html">vector_bool2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaedb70027d89a0a405046aefda4eabaa6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, bool, highp &gt; highp_bvec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of high qualifier bool numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00194_source.html#l00016">16</a> of file <a class="el" href="a00194_source.html">vector_bool3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaee663ff64429443ab07a5327074192f6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, bool, highp &gt; highp_bvec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of high qualifier bool numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00196_source.html#l00016">16</a> of file <a class="el" href="a00196_source.html">vector_bool4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gab98d77cca255914f5e29697fcbc2d975"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, f64, highp &gt; highp_dvec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of high double-qualifier floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00201_source.html#l00016">16</a> of file <a class="el" href="a00201_source.html">vector_double2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gab24dc20dcdc5b71282634bdbf6b70105"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, f64, highp &gt; highp_dvec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of high double-qualifier floating-point numbers. </p>
<p>There is no guarantee on the actual qualifier.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00203_source.html#l00017">17</a> of file <a class="el" href="a00203_source.html">vector_double3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gab654f4ed4a99d64a6cfc65320c2a7590"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, f64, highp &gt; highp_dvec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of high double-qualifier floating-point numbers. </p>
<p>There is no guarantee on the actual qualifier.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00205_source.html#l00018">18</a> of file <a class="el" href="a00205_source.html">vector_double4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaa18f6b80b41c214f10666948539c1f93"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, i32, highp &gt; highp_ivec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of high qualifier signed integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00217_source.html#l00016">16</a> of file <a class="el" href="a00217_source.html">vector_int2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga7dd782c3ef5719bc6d5c3ca826b8ad18"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, i32, highp &gt; highp_ivec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of high qualifier signed integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00219_source.html#l00016">16</a> of file <a class="el" href="a00219_source.html">vector_int3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gafb84dccdf5d82443df3ffc8428dcaf3e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, i32, highp &gt; highp_ivec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of high qualifier signed integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00221_source.html#l00016">16</a> of file <a class="el" href="a00221_source.html">vector_int4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gad5dd50da9e37387ca6b4e6f9c80fe6f8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, u32, highp &gt; highp_uvec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of high qualifier unsigned integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00229_source.html#l00016">16</a> of file <a class="el" href="a00229_source.html">vector_uint2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaef61508dd40ec523416697982f9ceaae"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, u32, highp &gt; highp_uvec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of high qualifier unsigned integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00231_source.html#l00016">16</a> of file <a class="el" href="a00231_source.html">vector_uint3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaeebd7dd9f3e678691f8620241e5f9221"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, u32, highp &gt; highp_uvec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of high qualifier unsigned integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00233_source.html#l00016">16</a> of file <a class="el" href="a00233_source.html">vector_uint4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaa92c1954d71b1e7914874bd787b43d1c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, float, highp &gt; highp_vec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of high single-qualifier floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00209_source.html#l00016">16</a> of file <a class="el" href="a00209_source.html">vector_float2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaca61dfaccbf2f58f2d8063a4e76b44a9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, float, highp &gt; highp_vec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of high single-qualifier floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00211_source.html#l00016">16</a> of file <a class="el" href="a00211_source.html">vector_float3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gad281decae52948b82feb3a9db8f63a7b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, float, highp &gt; highp_vec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of high single-qualifier floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00213_source.html#l00016">16</a> of file <a class="el" href="a00213_source.html">vector_float4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5a5452140650988b94d5716e4d872465"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, bool, lowp &gt; lowp_bvec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of low qualifier bool numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00192_source.html#l00028">28</a> of file <a class="el" href="a00192_source.html">vector_bool2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga79e0922a977662a8fd39d7829be3908b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, bool, lowp &gt; lowp_bvec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of low qualifier bool numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00194_source.html#l00028">28</a> of file <a class="el" href="a00194_source.html">vector_bool3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga15ac87724048ab7169bb5d3572939dd3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, bool, lowp &gt; lowp_bvec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of low qualifier bool numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00196_source.html#l00028">28</a> of file <a class="el" href="a00196_source.html">vector_bool4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga108086730d086b7f6f7a033955dfb9c3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, f64, lowp &gt; lowp_dvec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of low double-qualifier floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00201_source.html#l00028">28</a> of file <a class="el" href="a00201_source.html">vector_double2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga42c518b2917e19ce6946a84c64a3a4b2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, f64, lowp &gt; lowp_dvec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of low double-qualifier floating-point numbers. </p>
<p>There is no guarantee on the actual qualifier.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00203_source.html#l00031">31</a> of file <a class="el" href="a00203_source.html">vector_double3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga0b4432cb8d910e406576d10d802e190d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, f64, lowp &gt; lowp_dvec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of low double-qualifier floating-point numbers. </p>
<p>There is no guarantee on the actual qualifier.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00205_source.html#l00032">32</a> of file <a class="el" href="a00205_source.html">vector_double4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga8433c6c1fdd80c0a83941d94aff73fa0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, i32, lowp &gt; lowp_ivec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of low qualifier signed integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00217_source.html#l00028">28</a> of file <a class="el" href="a00217_source.html">vector_int2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gac1a86a75b3c68ebb704d7094043669d6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, i32, lowp &gt; lowp_ivec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of low qualifier signed integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00219_source.html#l00028">28</a> of file <a class="el" href="a00219_source.html">vector_int3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga27fc23da61859cd6356326c5f1c796de"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, i32, lowp &gt; lowp_ivec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of low qualifier signed integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00221_source.html#l00028">28</a> of file <a class="el" href="a00221_source.html">vector_int4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga752ee45136011301b64afd8c310c47a4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, u32, lowp &gt; lowp_uvec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of low qualifier unsigned integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00229_source.html#l00028">28</a> of file <a class="el" href="a00229_source.html">vector_uint2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga7b2efbdd6bdc2f8250c57f3e5dc9a292"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, u32, lowp &gt; lowp_uvec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of low qualifier unsigned integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00231_source.html#l00028">28</a> of file <a class="el" href="a00231_source.html">vector_uint3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5e6a632ec1165cf9f54ceeaa5e9b2b1e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, u32, lowp &gt; lowp_uvec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of low qualifier unsigned integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00233_source.html#l00028">28</a> of file <a class="el" href="a00233_source.html">vector_uint4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga30e8baef5d56d5c166872a2bc00f36e9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, float, lowp &gt; lowp_vec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of low single-qualifier floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00209_source.html#l00028">28</a> of file <a class="el" href="a00209_source.html">vector_float2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga868e8e4470a3ef97c7ee3032bf90dc79"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, float, lowp &gt; lowp_vec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of low single-qualifier floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00211_source.html#l00028">28</a> of file <a class="el" href="a00211_source.html">vector_float3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gace3acb313c800552a9411953eb8b2ed7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, float, lowp &gt; lowp_vec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of low single-qualifier floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00213_source.html#l00028">28</a> of file <a class="el" href="a00213_source.html">vector_float4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga1e743764869efa9223c2bcefccedaddc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, bool, mediump &gt; mediump_bvec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of medium qualifier bool numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00192_source.html#l00022">22</a> of file <a class="el" href="a00192_source.html">vector_bool2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga50c783c25082882ef00fe2e5cddba4aa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, bool, mediump &gt; mediump_bvec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of medium qualifier bool numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00194_source.html#l00022">22</a> of file <a class="el" href="a00194_source.html">vector_bool3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga0be2c682258604a35004f088782a9645"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, bool, mediump &gt; mediump_bvec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of medium qualifier bool numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00196_source.html#l00022">22</a> of file <a class="el" href="a00196_source.html">vector_bool4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga2f4f6e9a69a0281d06940fd0990cafc3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, f64, mediump &gt; mediump_dvec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of medium double-qualifier floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00201_source.html#l00022">22</a> of file <a class="el" href="a00201_source.html">vector_double2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga61c3b1dff4ec7c878af80503141b9f37"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, f64, mediump &gt; mediump_dvec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of medium double-qualifier floating-point numbers. </p>
<p>There is no guarantee on the actual qualifier.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00203_source.html#l00024">24</a> of file <a class="el" href="a00203_source.html">vector_double3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga23a8bca00914a51542bfea13a4778186"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, f64, mediump &gt; mediump_dvec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of medium double-qualifier floating-point numbers. </p>
<p>There is no guarantee on the actual qualifier.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00205_source.html#l00025">25</a> of file <a class="el" href="a00205_source.html">vector_double4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gac57496299d276ed97044074097bd5e2c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, i32, mediump &gt; mediump_ivec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of medium qualifier signed integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00217_source.html#l00022">22</a> of file <a class="el" href="a00217_source.html">vector_int2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga27cfb51e0dbe15bba27a14a8590e8466"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, i32, mediump &gt; mediump_ivec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of medium qualifier signed integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00219_source.html#l00022">22</a> of file <a class="el" href="a00219_source.html">vector_int3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga92a204c37e66ac6c1dc7ae91142f2ea5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, i32, mediump &gt; mediump_ivec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of medium qualifier signed integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00221_source.html#l00022">22</a> of file <a class="el" href="a00221_source.html">vector_int4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaa3b4f7806dad03d83bb3da0baa1e3b9b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, u32, mediump &gt; mediump_uvec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of medium qualifier unsigned integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00229_source.html#l00022">22</a> of file <a class="el" href="a00229_source.html">vector_uint2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga83b7df38feefbb357f3673d950fafef7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, u32, mediump &gt; mediump_uvec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of medium qualifier unsigned integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00231_source.html#l00022">22</a> of file <a class="el" href="a00231_source.html">vector_uint3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga64ed0deb6573375b7016daf82ffd53a7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, u32, mediump &gt; mediump_uvec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of medium qualifier unsigned integer numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00233_source.html#l00022">22</a> of file <a class="el" href="a00233_source.html">vector_uint4_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gabc61976261c406520c7a8e4d946dc3f0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 2, float, mediump &gt; mediump_vec2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 components vector of medium single-qualifier floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00209_source.html#l00022">22</a> of file <a class="el" href="a00209_source.html">vector_float2_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga2384e263df19f1404b733016eff78fca"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 3, float, mediump &gt; mediump_vec3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 components vector of medium single-qualifier floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00211_source.html#l00022">22</a> of file <a class="el" href="a00211_source.html">vector_float3_precision.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5c6978d3ffba06738416a33083853fc0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt; 4, float, mediump &gt; mediump_vec4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 components vector of medium single-qualifier floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.5 Vectors</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.7.2 Precision Qualifier</a> </dd></dl>

<p>Definition at line <a class="el" href="a00213_source.html#l00022">22</a> of file <a class="el" href="a00213_source.html">vector_float4_precision.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
