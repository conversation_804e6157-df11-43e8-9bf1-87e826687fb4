<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_norm</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_norm<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00113.html" title="GLM_GTX_norm ">glm/gtx/norm.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga85660f1b79f66c09c7b5a6f80e68c89f"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga85660f1b79f66c09c7b5a6f80e68c89f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#ga85660f1b79f66c09c7b5a6f80e68c89f">distance2</a> (vec&lt; L, T, Q &gt; const &amp;p0, vec&lt; L, T, Q &gt; const &amp;p1)</td></tr>
<tr class="memdesc:ga85660f1b79f66c09c7b5a6f80e68c89f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the squared distance between p0 and p1, i.e., length2(p0 - p1).  <a href="a00343.html#ga85660f1b79f66c09c7b5a6f80e68c89f">More...</a><br /></td></tr>
<tr class="separator:ga85660f1b79f66c09c7b5a6f80e68c89f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae2fc0b2aa967bebfd6a244700bff6997"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae2fc0b2aa967bebfd6a244700bff6997"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#gae2fc0b2aa967bebfd6a244700bff6997">l1Norm</a> (vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gae2fc0b2aa967bebfd6a244700bff6997"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the L1 norm between x and y.  <a href="a00343.html#gae2fc0b2aa967bebfd6a244700bff6997">More...</a><br /></td></tr>
<tr class="separator:gae2fc0b2aa967bebfd6a244700bff6997"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1a7491e2037ceeb37f83ce41addfc0be"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1a7491e2037ceeb37f83ce41addfc0be"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#ga1a7491e2037ceeb37f83ce41addfc0be">l1Norm</a> (vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga1a7491e2037ceeb37f83ce41addfc0be"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the L1 norm of v.  <a href="a00343.html#ga1a7491e2037ceeb37f83ce41addfc0be">More...</a><br /></td></tr>
<tr class="separator:ga1a7491e2037ceeb37f83ce41addfc0be"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga41340b2ef40a9307ab0f137181565168"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga41340b2ef40a9307ab0f137181565168"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#ga41340b2ef40a9307ab0f137181565168">l2Norm</a> (vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga41340b2ef40a9307ab0f137181565168"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the L2 norm between x and y.  <a href="a00343.html#ga41340b2ef40a9307ab0f137181565168">More...</a><br /></td></tr>
<tr class="separator:ga41340b2ef40a9307ab0f137181565168"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae288bde8f0e41fb4ed62e65137b18cba"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae288bde8f0e41fb4ed62e65137b18cba"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#gae288bde8f0e41fb4ed62e65137b18cba">l2Norm</a> (vec&lt; 3, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gae288bde8f0e41fb4ed62e65137b18cba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the L2 norm of v.  <a href="a00343.html#gae288bde8f0e41fb4ed62e65137b18cba">More...</a><br /></td></tr>
<tr class="separator:gae288bde8f0e41fb4ed62e65137b18cba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8d1789651050adb7024917984b41c3de"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8d1789651050adb7024917984b41c3de"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#ga8d1789651050adb7024917984b41c3de">length2</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga8d1789651050adb7024917984b41c3de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the squared length of x.  <a href="a00343.html#ga8d1789651050adb7024917984b41c3de">More...</a><br /></td></tr>
<tr class="separator:ga8d1789651050adb7024917984b41c3de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad58a8231fc32e38104a9e1c4d3c0cb64"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad58a8231fc32e38104a9e1c4d3c0cb64"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#gad58a8231fc32e38104a9e1c4d3c0cb64">lMaxNorm</a> (vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gad58a8231fc32e38104a9e1c4d3c0cb64"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the LMax norm between x and y.  <a href="a00343.html#gad58a8231fc32e38104a9e1c4d3c0cb64">More...</a><br /></td></tr>
<tr class="separator:gad58a8231fc32e38104a9e1c4d3c0cb64"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6968a324837a8e899396d44de23d5aae"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga6968a324837a8e899396d44de23d5aae"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#ga6968a324837a8e899396d44de23d5aae">lMaxNorm</a> (vec&lt; 3, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga6968a324837a8e899396d44de23d5aae"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the LMax norm of v.  <a href="a00343.html#ga6968a324837a8e899396d44de23d5aae">More...</a><br /></td></tr>
<tr class="separator:ga6968a324837a8e899396d44de23d5aae"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacad23d30497eb16f67709f2375d1f66a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gacad23d30497eb16f67709f2375d1f66a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#gacad23d30497eb16f67709f2375d1f66a">lxNorm</a> (vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y, unsigned int Depth)</td></tr>
<tr class="memdesc:gacad23d30497eb16f67709f2375d1f66a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the L norm between x and y.  <a href="a00343.html#gacad23d30497eb16f67709f2375d1f66a">More...</a><br /></td></tr>
<tr class="separator:gacad23d30497eb16f67709f2375d1f66a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac61b6d81d796d6eb4d4183396a19ab91"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac61b6d81d796d6eb4d4183396a19ab91"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00343.html#gac61b6d81d796d6eb4d4183396a19ab91">lxNorm</a> (vec&lt; 3, T, Q &gt; const &amp;x, unsigned int Depth)</td></tr>
<tr class="memdesc:gac61b6d81d796d6eb4d4183396a19ab91"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the L norm of v.  <a href="a00343.html#gac61b6d81d796d6eb4d4183396a19ab91">More...</a><br /></td></tr>
<tr class="separator:gac61b6d81d796d6eb4d4183396a19ab91"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00113.html" title="GLM_GTX_norm ">glm/gtx/norm.hpp</a>&gt; to use the features of this extension. </p>
<p>Various ways to compute vector norms. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga85660f1b79f66c09c7b5a6f80e68c89f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::distance2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>p0</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>p1</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the squared distance between p0 and p1, i.e., length2(p0 - p1). </p>
<p>From GLM_GTX_norm extension. </p>

</div>
</div>
<a class="anchor" id="gae2fc0b2aa967bebfd6a244700bff6997"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::l1Norm </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the L1 norm between x and y. </p>
<p>From GLM_GTX_norm extension. </p>

</div>
</div>
<a class="anchor" id="ga1a7491e2037ceeb37f83ce41addfc0be"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::l1Norm </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the L1 norm of v. </p>
<p>From GLM_GTX_norm extension. </p>

</div>
</div>
<a class="anchor" id="ga41340b2ef40a9307ab0f137181565168"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::l2Norm </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the L2 norm between x and y. </p>
<p>From GLM_GTX_norm extension. </p>

</div>
</div>
<a class="anchor" id="gae288bde8f0e41fb4ed62e65137b18cba"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::l2Norm </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the L2 norm of v. </p>
<p>From GLM_GTX_norm extension. </p>

</div>
</div>
<a class="anchor" id="ga8d1789651050adb7024917984b41c3de"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::length2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the squared length of x. </p>
<p>From GLM_GTX_norm extension. </p>

</div>
</div>
<a class="anchor" id="gad58a8231fc32e38104a9e1c4d3c0cb64"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::lMaxNorm </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the LMax norm between x and y. </p>
<p>From GLM_GTX_norm extension. </p>

</div>
</div>
<a class="anchor" id="ga6968a324837a8e899396d44de23d5aae"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::lMaxNorm </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the LMax norm of v. </p>
<p>From GLM_GTX_norm extension. </p>

</div>
</div>
<a class="anchor" id="gacad23d30497eb16f67709f2375d1f66a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::lxNorm </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned int&#160;</td>
          <td class="paramname"><em>Depth</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the L norm between x and y. </p>
<p>From GLM_GTX_norm extension. </p>

</div>
</div>
<a class="anchor" id="gac61b6d81d796d6eb4d4183396a19ab91"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::lxNorm </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">unsigned int&#160;</td>
          <td class="paramname"><em>Depth</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the L norm of v. </p>
<p>From GLM_GTX_norm extension. </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
