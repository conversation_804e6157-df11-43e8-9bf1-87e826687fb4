<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: type_mat3x4.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_033f5edb0915b828d2c46ed4804e5503.html">detail</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">type_mat3x4.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00170.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00004"></a><span class="lineno">    4</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00005"></a><span class="lineno">    5</span>&#160;</div>
<div class="line"><a name="l00006"></a><span class="lineno">    6</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00180.html">type_vec3.hpp</a>&quot;</span></div>
<div class="line"><a name="l00007"></a><span class="lineno">    7</span>&#160;<span class="preprocessor">#include &quot;<a class="code" href="a00181.html">type_vec4.hpp</a>&quot;</span></div>
<div class="line"><a name="l00008"></a><span class="lineno">    8</span>&#160;<span class="preprocessor">#include &lt;limits&gt;</span></div>
<div class="line"><a name="l00009"></a><span class="lineno">    9</span>&#160;<span class="preprocessor">#include &lt;cstddef&gt;</span></div>
<div class="line"><a name="l00010"></a><span class="lineno">   10</span>&#160;</div>
<div class="line"><a name="l00011"></a><span class="lineno">   11</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00012"></a><span class="lineno">   12</span>&#160;{</div>
<div class="line"><a name="l00013"></a><span class="lineno">   13</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;        <span class="keyword">struct </span>mat&lt;3, 4, T, Q&gt;</div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;        {</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;                <span class="keyword">typedef</span> vec&lt;4, T, Q&gt; col_type;</div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;                <span class="keyword">typedef</span> vec&lt;3, T, Q&gt; row_type;</div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;                <span class="keyword">typedef</span> mat&lt;3, 4, T, Q&gt; type;</div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;                <span class="keyword">typedef</span> mat&lt;4, 3, T, Q&gt; transpose_type;</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;                <span class="keyword">typedef</span> T value_type;</div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;        <span class="keyword">private</span>:</div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;                col_type value[3];</div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;        <span class="keyword">public</span>:</div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;                <span class="comment">// -- Accesses --</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;                <span class="keyword">typedef</span> length_t length_type;</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;                GLM_FUNC_DECL <span class="keyword">static</span> GLM_CONSTEXPR length_type <a class="code" href="a00254.html#gab703732449be6c7199369b3f9a91ed38">length</a>() { <span class="keywordflow">return</span> 3; }</div>
<div class="line"><a name="l00030"></a><span class="lineno">   30</span>&#160;</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;                GLM_FUNC_DECL col_type &amp; operator[](length_type i);</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR col_type <span class="keyword">const</span>&amp; operator[](length_type i) <span class="keyword">const</span>;</div>
<div class="line"><a name="l00033"></a><span class="lineno">   33</span>&#160;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;                <span class="comment">// -- Constructors --</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR mat() GLM_DEFAULT;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;                template&lt;qualifier P&gt;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR mat(mat&lt;3, 4, T, P&gt; const&amp; m);</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;                GLM_FUNC_DECL explicit GLM_CONSTEXPR mat(T scalar);</div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR mat(</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;                        T x0, T y0, T z0, T w0,</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;                        T x1, T y1, T z1, T w1,</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;                        T x2, T y2, T z2, T w2);</div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR mat(</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;                        col_type const&amp; v0,</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;                        col_type const&amp; v1,</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;                        col_type const&amp; v2);</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;                <span class="comment">// -- Conversions --</span></div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;                template&lt;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;                        typename X1, typename Y1, typename Z1, typename W1,</div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;                        typename X2, typename Y2, typename Z2, typename W2,</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;                        typename X3, typename Y3, typename Z3, typename W3&gt;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR mat(</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;                        X1 x1, Y1 y1, Z1 z1, W1 w1,</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;                        X2 x2, Y2 y2, Z2 z2, W2 w2,</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;                        X3 x3, Y3 y3, Z3 z3, W3 w3);</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;                template&lt;typename V1, typename V2, typename V3&gt;</div>
<div class="line"><a name="l00062"></a><span class="lineno">   62</span>&#160;                GLM_FUNC_DECL GLM_CONSTEXPR mat(</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;                        vec&lt;4, V1, Q&gt; const&amp; v1,</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;                        vec&lt;4, V2, Q&gt; const&amp; v2,</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;                        vec&lt;4, V3, Q&gt; const&amp; v3);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;                <span class="comment">// -- Matrix conversions --</span></div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;                template&lt;typename U, qualifier P&gt;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;                GLM_FUNC_DECL GLM_EXPLICIT GLM_CONSTEXPR mat(mat&lt;3, 4, U, P&gt; const&amp; m);</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;                GLM_FUNC_DECL GLM_EXPLICIT GLM_CONSTEXPR mat(mat&lt;2, 2, T, Q&gt; const&amp; x);</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;                GLM_FUNC_DECL GLM_EXPLICIT GLM_CONSTEXPR mat(mat&lt;3, 3, T, Q&gt; const&amp; x);</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;                GLM_FUNC_DECL GLM_EXPLICIT GLM_CONSTEXPR mat(mat&lt;4, 4, T, Q&gt; const&amp; x);</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;                GLM_FUNC_DECL GLM_EXPLICIT GLM_CONSTEXPR mat(mat&lt;2, 3, T, Q&gt; const&amp; x);</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;                GLM_FUNC_DECL GLM_EXPLICIT GLM_CONSTEXPR mat(mat&lt;3, 2, T, Q&gt; const&amp; x);</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;                GLM_FUNC_DECL GLM_EXPLICIT GLM_CONSTEXPR mat(mat&lt;2, 4, T, Q&gt; const&amp; x);</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;                GLM_FUNC_DECL GLM_EXPLICIT GLM_CONSTEXPR mat(mat&lt;4, 2, T, Q&gt; const&amp; x);</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;                GLM_FUNC_DECL GLM_EXPLICIT GLM_CONSTEXPR mat(mat&lt;4, 3, T, Q&gt; const&amp; x);</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;                <span class="comment">// -- Unary arithmetic operators --</span></div>
<div class="line"><a name="l00082"></a><span class="lineno">   82</span>&#160;</div>
<div class="line"><a name="l00083"></a><span class="lineno">   83</span>&#160;                template&lt;typename U&gt;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;                GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; &amp; operator=(mat&lt;3, 4, U, Q&gt; const&amp; m);</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;                template&lt;typename U&gt;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;                GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; &amp; operator+=(U s);</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;                template&lt;typename U&gt;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;                GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; &amp; operator+=(mat&lt;3, 4, U, Q&gt; const&amp; m);</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;                template&lt;typename U&gt;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;                GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; &amp; operator-=(U s);</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;                template&lt;typename U&gt;</div>
<div class="line"><a name="l00092"></a><span class="lineno">   92</span>&#160;                GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; &amp; operator-=(mat&lt;3, 4, U, Q&gt; const&amp; m);</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;                template&lt;typename U&gt;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;                GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; &amp; operator*=(U s);</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;                template&lt;typename U&gt;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;                GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; &amp; operator/=(U s);</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;                <span class="comment">// -- Increment and decrement operators --</span></div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;                GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; &amp; operator++();</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;                GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; &amp; operator--();</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;                GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator++(<span class="keywordtype">int</span>);</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;                GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator--(<span class="keywordtype">int</span>);</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        };</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        <span class="comment">// -- Unary operators --</span></div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div>
<div class="line"><a name="l00108"></a><span class="lineno">  108</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator+(mat&lt;3, 4, T, Q&gt; const&amp; m);</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00112"></a><span class="lineno">  112</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator-(mat&lt;3, 4, T, Q&gt; const&amp; m);</div>
<div class="line"><a name="l00113"></a><span class="lineno">  113</span>&#160;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;        <span class="comment">// -- Binary operators --</span></div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator+(mat&lt;3, 4, T, Q&gt; const&amp; m, T scalar);</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator+(mat&lt;3, 4, T, Q&gt; const&amp; m1, mat&lt;3, 4, T, Q&gt; const&amp; m2);</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div>
<div class="line"><a name="l00122"></a><span class="lineno">  122</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator-(mat&lt;3, 4, T, Q&gt; const&amp; m, T scalar);</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator-(mat&lt;3, 4, T, Q&gt; const&amp; m1, mat&lt;3, 4, T, Q&gt; const&amp; m2);</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator*(mat&lt;3, 4, T, Q&gt; const&amp; m, T scalar);</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator*(T scalar, mat&lt;3, 4, T, Q&gt; const&amp; m);</div>
<div class="line"><a name="l00133"></a><span class="lineno">  133</span>&#160;</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;        GLM_FUNC_DECL typename mat&lt;3, 4, T, Q&gt;::col_type operator*(mat&lt;3, 4, T, Q&gt; const&amp; m, typename mat&lt;3, 4, T, Q&gt;::row_type const&amp; v);</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00138"></a><span class="lineno">  138</span>&#160;        GLM_FUNC_DECL typename mat&lt;3, 4, T, Q&gt;::row_type operator*(typename mat&lt;3, 4, T, Q&gt;::col_type const&amp; v, mat&lt;3, 4, T, Q&gt; const&amp; m);</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; operator*(mat&lt;3, 4, T, Q&gt; const&amp; m1,      mat&lt;4, 3, T, Q&gt; const&amp; m2);</div>
<div class="line"><a name="l00142"></a><span class="lineno">  142</span>&#160;</div>
<div class="line"><a name="l00143"></a><span class="lineno">  143</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        GLM_FUNC_DECL mat&lt;2, 4, T, Q&gt; operator*(mat&lt;3, 4, T, Q&gt; const&amp; m1, mat&lt;2, 3, T, Q&gt; const&amp; m2);</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00147"></a><span class="lineno">  147</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator*(mat&lt;3, 4, T, Q&gt; const&amp; m1,      mat&lt;3, 3, T, Q&gt; const&amp; m2);</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator/(mat&lt;3, 4, T, Q&gt; const&amp; m, T scalar);</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; operator/(T scalar, mat&lt;3, 4, T, Q&gt; const&amp; m);</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        <span class="comment">// -- Boolean operators --</span></div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">bool</span> operator==(mat&lt;3, 4, T, Q&gt; const&amp; m1, mat&lt;3, 4, T, Q&gt; const&amp; m2);</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        template&lt;typename T, qualifier Q&gt;</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;        GLM_FUNC_DECL <span class="keywordtype">bool</span> operator!=(mat&lt;3, 4, T, Q&gt; const&amp; m1, mat&lt;3, 4, T, Q&gt; const&amp; m2);</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00163"></a><span class="lineno">  163</span>&#160;</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;<span class="preprocessor">#ifndef GLM_EXTERNAL_TEMPLATE</span></div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;<span class="preprocessor">#include &quot;type_mat3x4.inl&quot;</span></div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="ttc" id="a00254_html_gab703732449be6c7199369b3f9a91ed38"><div class="ttname"><a href="a00254.html#gab703732449be6c7199369b3f9a91ed38">glm::length</a></div><div class="ttdeci">GLM_FUNC_DECL T length(qua&lt; T, Q &gt; const &amp;q)</div><div class="ttdoc">Returns the norm of a quaternions. </div></div>
<div class="ttc" id="a00181_html"><div class="ttname"><a href="a00181.html">type_vec4.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00180_html"><div class="ttname"><a href="a00180.html">type_vec3.hpp</a></div><div class="ttdoc">Core features </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
