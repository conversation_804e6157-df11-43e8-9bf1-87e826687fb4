<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: vector_relational.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">vector_relational.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00225.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;</div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#include &quot;detail/qualifier.hpp&quot;</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#include &quot;detail/setup.hpp&quot;</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;</div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;{</div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;</div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; <a class="code" href="a00374.html#gae90ed1592c395f93e3f3dfce6b2f39c6">lessThan</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; <a class="code" href="a00374.html#gab0bdafc019d227257ff73fb5bcca1718">lessThanEqual</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; <a class="code" href="a00374.html#gadfdb8ea82deca869ddc7e63ea5a63ae4">greaterThan</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; <a class="code" href="a00374.html#ga859975f538940f8d18fe62f916b9abd7">greaterThanEqual</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00078"></a><span class="lineno">   78</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; <a class="code" href="a00374.html#gab4c5cfdaa70834421397a85aa83ad946">equal</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;</div>
<div class="line"><a name="l00087"></a><span class="lineno">   87</span>&#160;        <span class="keyword">template</span>&lt;length_t L, <span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00088"></a><span class="lineno">   88</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; <a class="code" href="a00374.html#ga17c19dc1b76cd5aef63e9e7ff3aa3c27">notEqual</a>(vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; x, vec&lt;L, T, Q&gt; <span class="keyword">const</span>&amp; y);</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR <span class="keywordtype">bool</span> <a class="code" href="a00374.html#ga911b3f8e41459dd551ccb6d385d91061">any</a>(vec&lt;L, bool, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR <span class="keywordtype">bool</span> <a class="code" href="a00374.html#ga87e53f50b679f5f95c5cb4780311b3dd">all</a>(vec&lt;L, bool, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;        <span class="keyword">template</span>&lt;length_t L, qualifier Q&gt;</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;        GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, bool, Q&gt; <a class="code" href="a00374.html#ga610fcd175791fd246e328ffee10dbf1e">not_</a>(vec&lt;L, bool, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00117"></a><span class="lineno">  117</span>&#160;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;<span class="preprocessor">#include &quot;detail/func_vector_relational.inl&quot;</span></div>
<div class="ttc" id="a00374_html_ga87e53f50b679f5f95c5cb4780311b3dd"><div class="ttname"><a href="a00374.html#ga87e53f50b679f5f95c5cb4780311b3dd">glm::all</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR bool all(vec&lt; L, bool, Q &gt; const &amp;v)</div><div class="ttdoc">Returns true if all components of x are true. </div></div>
<div class="ttc" id="a00374_html_gadfdb8ea82deca869ddc7e63ea5a63ae4"><div class="ttname"><a href="a00374.html#gadfdb8ea82deca869ddc7e63ea5a63ae4">glm::greaterThan</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt; greaterThan(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns the component-wise comparison of result x > y. </div></div>
<div class="ttc" id="a00374_html_ga17c19dc1b76cd5aef63e9e7ff3aa3c27"><div class="ttname"><a href="a00374.html#ga17c19dc1b76cd5aef63e9e7ff3aa3c27">glm::notEqual</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt; notEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns the component-wise comparison of result x != y. </div></div>
<div class="ttc" id="a00374_html_gab0bdafc019d227257ff73fb5bcca1718"><div class="ttname"><a href="a00374.html#gab0bdafc019d227257ff73fb5bcca1718">glm::lessThanEqual</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt; lessThanEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns the component-wise comparison of result x <= y. </div></div>
<div class="ttc" id="a00374_html_ga610fcd175791fd246e328ffee10dbf1e"><div class="ttname"><a href="a00374.html#ga610fcd175791fd246e328ffee10dbf1e">glm::not_</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt; not_(vec&lt; L, bool, Q &gt; const &amp;v)</div><div class="ttdoc">Returns the component-wise logical complement of x. </div></div>
<div class="ttc" id="a00374_html_ga911b3f8e41459dd551ccb6d385d91061"><div class="ttname"><a href="a00374.html#ga911b3f8e41459dd551ccb6d385d91061">glm::any</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR bool any(vec&lt; L, bool, Q &gt; const &amp;v)</div><div class="ttdoc">Returns true if any component of x is true. </div></div>
<div class="ttc" id="a00374_html_gab4c5cfdaa70834421397a85aa83ad946"><div class="ttname"><a href="a00374.html#gab4c5cfdaa70834421397a85aa83ad946">glm::equal</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt; equal(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns the component-wise comparison of result x == y. </div></div>
<div class="ttc" id="a00374_html_ga859975f538940f8d18fe62f916b9abd7"><div class="ttname"><a href="a00374.html#ga859975f538940f8d18fe62f916b9abd7">glm::greaterThanEqual</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt; greaterThanEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns the component-wise comparison of result x >= y. </div></div>
<div class="ttc" id="a00374_html_gae90ed1592c395f93e3f3dfce6b2f39c6"><div class="ttname"><a href="a00374.html#gae90ed1592c395f93e3f3dfce6b2f39c6">glm::lessThan</a></div><div class="ttdeci">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt; lessThan(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</div><div class="ttdoc">Returns the component-wise comparison result of x < y. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
