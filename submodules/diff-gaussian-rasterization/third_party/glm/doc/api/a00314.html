<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_common</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_common<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00016.html" title="GLM_GTX_common ">glm/gtx/common.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gab7d89c14c48ad01f720fb5daf8813161"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab7d89c14c48ad01f720fb5daf8813161"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00314.html#gab7d89c14c48ad01f720fb5daf8813161">closeBounded</a> (vec&lt; L, T, Q &gt; const &amp;Value, vec&lt; L, T, Q &gt; const &amp;Min, vec&lt; L, T, Q &gt; const &amp;Max)</td></tr>
<tr class="memdesc:gab7d89c14c48ad01f720fb5daf8813161"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether vector components values are within an interval.  <a href="a00314.html#gab7d89c14c48ad01f720fb5daf8813161">More...</a><br /></td></tr>
<tr class="separator:gab7d89c14c48ad01f720fb5daf8813161"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae5e80425df9833164ad469e83b475fb4"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae5e80425df9833164ad469e83b475fb4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00314.html#gae5e80425df9833164ad469e83b475fb4">fmod</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gae5e80425df9833164ad469e83b475fb4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Similar to 'mod' but with a different rounding and integer support.  <a href="a00314.html#gae5e80425df9833164ad469e83b475fb4">More...</a><br /></td></tr>
<tr class="separator:gae5e80425df9833164ad469e83b475fb4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga74aa7c7462245d83bd5a9edf9c6c2d91"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga74aa7c7462245d83bd5a9edf9c6c2d91"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType::bool_type&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00314.html#ga74aa7c7462245d83bd5a9edf9c6c2d91">isdenormal</a> (genType const &amp;x)</td></tr>
<tr class="memdesc:ga74aa7c7462245d83bd5a9edf9c6c2d91"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if x is a denormalized number Numbers whose absolute value is too small to be represented in the normal format are represented in an alternate, denormalized format.  <a href="a00314.html#ga74aa7c7462245d83bd5a9edf9c6c2d91">More...</a><br /></td></tr>
<tr class="separator:ga74aa7c7462245d83bd5a9edf9c6c2d91"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafd303042ba2ba695bf53b2315f53f93f"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gafd303042ba2ba695bf53b2315f53f93f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00314.html#gafd303042ba2ba695bf53b2315f53f93f">openBounded</a> (vec&lt; L, T, Q &gt; const &amp;Value, vec&lt; L, T, Q &gt; const &amp;Min, vec&lt; L, T, Q &gt; const &amp;Max)</td></tr>
<tr class="memdesc:gafd303042ba2ba695bf53b2315f53f93f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns whether vector components values are within an interval.  <a href="a00314.html#gafd303042ba2ba695bf53b2315f53f93f">More...</a><br /></td></tr>
<tr class="separator:gafd303042ba2ba695bf53b2315f53f93f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00016.html" title="GLM_GTX_common ">glm/gtx/common.hpp</a>&gt; to use the features of this extension. </p>
<p>Provide functions to increase the compatibility with Cg and HLSL languages </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gab7d89c14c48ad01f720fb5daf8813161"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, bool, Q&gt; glm::closeBounded </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Min</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Max</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns whether vector components values are within an interval. </p>
<p>A closed interval includes its endpoints, and is denoted with square brackets.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00275.html" title="Exposes comparison functions for vector types that take a user defined epsilon values. ">GLM_EXT_vector_relational</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae5e80425df9833164ad469e83b475fb4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fmod </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Similar to 'mod' but with a different rounding and integer support. </p>
<p>Returns 'x - y * trunc(x/y)' instead of 'x - y * floor(x/y)'</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://stackoverflow.com/questions/7610631/glsl-mod-vs-hlsl-fmod">GLSL mod vs HLSL fmod</a> </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/mod.xml">GLSL mod man page</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga74aa7c7462245d83bd5a9edf9c6c2d91"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType::bool_type glm::isdenormal </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if x is a denormalized number Numbers whose absolute value is too small to be represented in the normal format are represented in an alternate, denormalized format. </p>
<p>This format is less precise but can represent values closer to zero.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/isnan.xml">GLSL isnan man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.3 Common Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafd303042ba2ba695bf53b2315f53f93f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, bool, Q&gt; glm::openBounded </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Min</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Max</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns whether vector components values are within an interval. </p>
<p>A open interval excludes its endpoints, and is denoted with square brackets.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00275.html" title="Exposes comparison functions for vector types that take a user defined epsilon values. ">GLM_EXT_vector_relational</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
