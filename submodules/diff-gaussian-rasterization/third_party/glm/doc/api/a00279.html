<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Geometric functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Geometric functions<div class="ingroups"><a class="el" href="a00280.html">Core features</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>These operate on vectors as vectors, not component-wise.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaeeec0794212fe84fc9d261de067c9587"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaeeec0794212fe84fc9d261de067c9587"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00279.html#gaeeec0794212fe84fc9d261de067c9587">cross</a> (vec&lt; 3, T, Q &gt; const &amp;x, vec&lt; 3, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gaeeec0794212fe84fc9d261de067c9587"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the cross product of x and y.  <a href="a00279.html#gaeeec0794212fe84fc9d261de067c9587">More...</a><br /></td></tr>
<tr class="separator:gaeeec0794212fe84fc9d261de067c9587"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa68de6c53e20dfb2dac2d20197562e3f"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa68de6c53e20dfb2dac2d20197562e3f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00279.html#gaa68de6c53e20dfb2dac2d20197562e3f">distance</a> (vec&lt; L, T, Q &gt; const &amp;p0, vec&lt; L, T, Q &gt; const &amp;p1)</td></tr>
<tr class="memdesc:gaa68de6c53e20dfb2dac2d20197562e3f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the distance betwwen p0 and p1, i.e., length(p0 - p1).  <a href="a00279.html#gaa68de6c53e20dfb2dac2d20197562e3f">More...</a><br /></td></tr>
<tr class="separator:gaa68de6c53e20dfb2dac2d20197562e3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaad6c5d9d39bdc0bf43baf1b22e147a0a"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaad6c5d9d39bdc0bf43baf1b22e147a0a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00279.html#gaad6c5d9d39bdc0bf43baf1b22e147a0a">dot</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gaad6c5d9d39bdc0bf43baf1b22e147a0a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the dot product of x and y, i.e., result = x * y.  <a href="a00279.html#gaad6c5d9d39bdc0bf43baf1b22e147a0a">More...</a><br /></td></tr>
<tr class="separator:gaad6c5d9d39bdc0bf43baf1b22e147a0a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7aed0a36c738169402404a3a5d54e43b"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga7aed0a36c738169402404a3a5d54e43b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00279.html#ga7aed0a36c738169402404a3a5d54e43b">faceforward</a> (vec&lt; L, T, Q &gt; const &amp;N, vec&lt; L, T, Q &gt; const &amp;I, vec&lt; L, T, Q &gt; const &amp;Nref)</td></tr>
<tr class="memdesc:ga7aed0a36c738169402404a3a5d54e43b"><td class="mdescLeft">&#160;</td><td class="mdescRight">If dot(Nref, I) &lt; 0.0, return N, otherwise, return -N.  <a href="a00279.html#ga7aed0a36c738169402404a3a5d54e43b">More...</a><br /></td></tr>
<tr class="separator:ga7aed0a36c738169402404a3a5d54e43b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0cdabbb000834d994a1d6dc56f8f5263"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0cdabbb000834d994a1d6dc56f8f5263"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00279.html#ga0cdabbb000834d994a1d6dc56f8f5263">length</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga0cdabbb000834d994a1d6dc56f8f5263"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the length of x, i.e., sqrt(x * x).  <a href="a00279.html#ga0cdabbb000834d994a1d6dc56f8f5263">More...</a><br /></td></tr>
<tr class="separator:ga0cdabbb000834d994a1d6dc56f8f5263"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3b8d3dcae77870781392ed2902cce597"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga3b8d3dcae77870781392ed2902cce597"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00279.html#ga3b8d3dcae77870781392ed2902cce597">normalize</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga3b8d3dcae77870781392ed2902cce597"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a vector in the same direction as x but with length of 1.  <a href="a00279.html#ga3b8d3dcae77870781392ed2902cce597">More...</a><br /></td></tr>
<tr class="separator:ga3b8d3dcae77870781392ed2902cce597"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5631dd1d5618de5450b1ea3cf3e94905"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5631dd1d5618de5450b1ea3cf3e94905"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00279.html#ga5631dd1d5618de5450b1ea3cf3e94905">reflect</a> (vec&lt; L, T, Q &gt; const &amp;I, vec&lt; L, T, Q &gt; const &amp;N)</td></tr>
<tr class="memdesc:ga5631dd1d5618de5450b1ea3cf3e94905"><td class="mdescLeft">&#160;</td><td class="mdescRight">For the incident vector I and surface orientation N, returns the reflection direction : result = I - 2.0 * dot(N, I) * N.  <a href="a00279.html#ga5631dd1d5618de5450b1ea3cf3e94905">More...</a><br /></td></tr>
<tr class="separator:ga5631dd1d5618de5450b1ea3cf3e94905"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga01da3dff9e2ef6b9d4915c3047e22b74"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga01da3dff9e2ef6b9d4915c3047e22b74"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00279.html#ga01da3dff9e2ef6b9d4915c3047e22b74">refract</a> (vec&lt; L, T, Q &gt; const &amp;I, vec&lt; L, T, Q &gt; const &amp;N, T eta)</td></tr>
<tr class="memdesc:ga01da3dff9e2ef6b9d4915c3047e22b74"><td class="mdescLeft">&#160;</td><td class="mdescRight">For the incident vector I and surface normal N, and the ratio of indices of refraction eta, return the refraction vector.  <a href="a00279.html#ga01da3dff9e2ef6b9d4915c3047e22b74">More...</a><br /></td></tr>
<tr class="separator:ga01da3dff9e2ef6b9d4915c3047e22b74"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>These operate on vectors as vectors, not component-wise. </p>
<p>Include &lt;<a class="el" href="a00036.html" title="Core features ">glm/geometric.hpp</a>&gt; to use these core features. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaeeec0794212fe84fc9d261de067c9587"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::cross </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the cross product of x and y. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/cross.xml">GLSL cross man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.5 Geometric Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa68de6c53e20dfb2dac2d20197562e3f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::distance </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>p0</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>p1</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the distance betwwen p0 and p1, i.e., length(p0 - p1). </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/distance.xml">GLSL distance man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.5 Geometric Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaad6c5d9d39bdc0bf43baf1b22e147a0a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::dot </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the dot product of x and y, i.e., result = x * y. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/dot.xml">GLSL dot man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.5 Geometric Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7aed0a36c738169402404a3a5d54e43b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::faceforward </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>N</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>I</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Nref</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>If dot(Nref, I) &lt; 0.0, return N, otherwise, return -N. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/faceforward.xml">GLSL faceforward man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.5 Geometric Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0cdabbb000834d994a1d6dc56f8f5263"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::length </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the length of x, i.e., sqrt(x * x). </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/length.xml">GLSL length man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.5 Geometric Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3b8d3dcae77870781392ed2902cce597"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::normalize </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a vector in the same direction as x but with length of 1. </p>
<p>According to issue 10 GLSL 1.10 specification, if length(x) == 0 then result is undefined and generate an error.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/normalize.xml">GLSL normalize man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.5 Geometric Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5631dd1d5618de5450b1ea3cf3e94905"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::reflect </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>I</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>N</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>For the incident vector I and surface orientation N, returns the reflection direction : result = I - 2.0 * dot(N, I) * N. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/reflect.xml">GLSL reflect man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.5 Geometric Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga01da3dff9e2ef6b9d4915c3047e22b74"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::refract </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>I</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>N</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>eta</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>For the incident vector I and surface normal N, and the ratio of indices of refraction eta, return the refraction vector. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>An integer between 1 and 4 included that qualify the dimension of the vector. </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/refract.xml">GLSL refract man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.5 Geometric Functions</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
