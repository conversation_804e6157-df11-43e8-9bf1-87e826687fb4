var searchData=
[
  ['nextmultiple',['nextMultiple',['../a00261.html#gab770a3835c44c8a6fd225be4f4e6b317',1,'glm::nextMultiple(genIUType v, genIUType Multiple)'],['../a00274.html#gace38d00601cbf49cd4dc03f003ab42b7',1,'glm::nextMultiple(vec&lt; L, T, Q &gt; const &amp;v, T Multiple)'],['../a00274.html#gacda365edad320c7aff19cc283a3b8ca2',1,'glm::nextMultiple(vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)']]],
  ['nextpoweroftwo',['nextPowerOfTwo',['../a00261.html#ga3a37c2f2fd347886c9af6a3ca3db04dc',1,'glm::nextPowerOfTwo(genIUType v)'],['../a00274.html#gabba67f8aac9915e10fca727277274502',1,'glm::nextPowerOfTwo(vec&lt; L, T, Q &gt; const &amp;v)']]],
  ['nlz',['nlz',['../a00330.html#ga78dff8bdb361bf0061194c93e003d189',1,'glm']]],
  ['normalize',['normalize',['../a00254.html#gabf30e3263fffe8dcc6659aea76ae8927',1,'glm::normalize(qua&lt; T, Q &gt; const &amp;q)'],['../a00279.html#ga3b8d3dcae77870781392ed2902cce597',1,'glm::normalize(vec&lt; L, T, Q &gt; const &amp;x)'],['../a00317.html#ga299b8641509606b1958ffa104a162cfe',1,'glm::normalize(tdualquat&lt; T, Q &gt; const &amp;q)']]],
  ['normalizedot',['normalizeDot',['../a00345.html#gacb140a2b903115d318c8b0a2fb5a5daa',1,'glm']]],
  ['not_5f',['not_',['../a00374.html#ga610fcd175791fd246e328ffee10dbf1e',1,'glm']]],
  ['notequal',['notEqual',['../a00246.html#ga8504f18a7e2bf315393032c2137dad83',1,'glm::notEqual(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y)'],['../a00246.html#ga29071147d118569344d10944b7d5c378',1,'glm::notEqual(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, T epsilon)'],['../a00246.html#gad7959e14fbc35b4ed2617daf4d67f6cd',1,'glm::notEqual(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, vec&lt; C, T, Q &gt; const &amp;epsilon)'],['../a00246.html#gaa1cd7fc228ef6e26c73583fd0d9c6552',1,'glm::notEqual(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, int ULPs)'],['../a00246.html#gaa5517341754149ffba742d230afd1f32',1,'glm::notEqual(mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y, vec&lt; C, int, Q &gt; const &amp;ULPs)'],['../a00255.html#gab441cee0de5867a868f3a586ee68cfe1',1,'glm::notEqual(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)'],['../a00255.html#ga5117a44c1bf21af857cd23e44a96d313',1,'glm::notEqual(qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T epsilon)'],['../a00275.html#ga4a99cc41341567567a608719449c1fac',1,'glm::notEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, T epsilon)'],['../a00275.html#ga417cf51304359db18e819dda9bce5767',1,'glm::notEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;epsilon)'],['../a00275.html#ga8b5c2c3f83422ae5b71fa960d03b0339',1,'glm::notEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, int ULPs)'],['../a00275.html#ga0b15ffe32987a6029b14398eb0def01a',1,'glm::notEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, int, Q &gt; const &amp;ULPs)'],['../a00374.html#ga17c19dc1b76cd5aef63e9e7ff3aa3c27',1,'glm::notEqual(vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)']]]
];
