<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_fast_exponential</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_fast_exponential<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00031.html" title="GLM_GTX_fast_exponential ">glm/gtx/fast_exponential.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaa3180ac8f96ab37ab96e0cacaf608e10"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaa3180ac8f96ab37ab96e0cacaf608e10"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00323.html#gaa3180ac8f96ab37ab96e0cacaf608e10">fastExp</a> (T x)</td></tr>
<tr class="memdesc:gaa3180ac8f96ab37ab96e0cacaf608e10"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common exp function but less accurate.  <a href="a00323.html#gaa3180ac8f96ab37ab96e0cacaf608e10">More...</a><br /></td></tr>
<tr class="separator:gaa3180ac8f96ab37ab96e0cacaf608e10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3ba6153aec6bd74628f8b00530aa8d58"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga3ba6153aec6bd74628f8b00530aa8d58"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00323.html#ga3ba6153aec6bd74628f8b00530aa8d58">fastExp</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga3ba6153aec6bd74628f8b00530aa8d58"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common exp function but less accurate.  <a href="a00323.html#ga3ba6153aec6bd74628f8b00530aa8d58">More...</a><br /></td></tr>
<tr class="separator:ga3ba6153aec6bd74628f8b00530aa8d58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0af50585955eb14c60bb286297fabab2"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga0af50585955eb14c60bb286297fabab2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00323.html#ga0af50585955eb14c60bb286297fabab2">fastExp2</a> (T x)</td></tr>
<tr class="memdesc:ga0af50585955eb14c60bb286297fabab2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common exp2 function but less accurate.  <a href="a00323.html#ga0af50585955eb14c60bb286297fabab2">More...</a><br /></td></tr>
<tr class="separator:ga0af50585955eb14c60bb286297fabab2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacaaed8b67d20d244b7de217e7816c1b6"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gacaaed8b67d20d244b7de217e7816c1b6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00323.html#gacaaed8b67d20d244b7de217e7816c1b6">fastExp2</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gacaaed8b67d20d244b7de217e7816c1b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common exp2 function but less accurate.  <a href="a00323.html#gacaaed8b67d20d244b7de217e7816c1b6">More...</a><br /></td></tr>
<tr class="separator:gacaaed8b67d20d244b7de217e7816c1b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae1bdc97b7f96a600e29c753f1cd4388a"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gae1bdc97b7f96a600e29c753f1cd4388a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00323.html#gae1bdc97b7f96a600e29c753f1cd4388a">fastLog</a> (T x)</td></tr>
<tr class="memdesc:gae1bdc97b7f96a600e29c753f1cd4388a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common log function but less accurate.  <a href="a00323.html#gae1bdc97b7f96a600e29c753f1cd4388a">More...</a><br /></td></tr>
<tr class="separator:gae1bdc97b7f96a600e29c753f1cd4388a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga937256993a7219e73f186bb348fe6be8"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga937256993a7219e73f186bb348fe6be8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00323.html#ga937256993a7219e73f186bb348fe6be8">fastLog</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga937256993a7219e73f186bb348fe6be8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common exp2 function but less accurate.  <a href="a00323.html#ga937256993a7219e73f186bb348fe6be8">More...</a><br /></td></tr>
<tr class="separator:ga937256993a7219e73f186bb348fe6be8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e98118685f6dc9e05fbb13dd5e5234e"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga6e98118685f6dc9e05fbb13dd5e5234e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00323.html#ga6e98118685f6dc9e05fbb13dd5e5234e">fastLog2</a> (T x)</td></tr>
<tr class="memdesc:ga6e98118685f6dc9e05fbb13dd5e5234e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common log2 function but less accurate.  <a href="a00323.html#ga6e98118685f6dc9e05fbb13dd5e5234e">More...</a><br /></td></tr>
<tr class="separator:ga6e98118685f6dc9e05fbb13dd5e5234e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7562043539194ccc24649f8475bc5584"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga7562043539194ccc24649f8475bc5584"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00323.html#ga7562043539194ccc24649f8475bc5584">fastLog2</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga7562043539194ccc24649f8475bc5584"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common log2 function but less accurate.  <a href="a00323.html#ga7562043539194ccc24649f8475bc5584">More...</a><br /></td></tr>
<tr class="separator:ga7562043539194ccc24649f8475bc5584"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5340e98a11fcbbd936ba6e983a154d50"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga5340e98a11fcbbd936ba6e983a154d50"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00323.html#ga5340e98a11fcbbd936ba6e983a154d50">fastPow</a> (genType x, genType y)</td></tr>
<tr class="memdesc:ga5340e98a11fcbbd936ba6e983a154d50"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common pow function but less accurate.  <a href="a00323.html#ga5340e98a11fcbbd936ba6e983a154d50">More...</a><br /></td></tr>
<tr class="separator:ga5340e98a11fcbbd936ba6e983a154d50"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga15325a8ed2d1c4ed2412c4b3b3927aa2"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga15325a8ed2d1c4ed2412c4b3b3927aa2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00323.html#ga15325a8ed2d1c4ed2412c4b3b3927aa2">fastPow</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga15325a8ed2d1c4ed2412c4b3b3927aa2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common pow function but less accurate.  <a href="a00323.html#ga15325a8ed2d1c4ed2412c4b3b3927aa2">More...</a><br /></td></tr>
<tr class="separator:ga15325a8ed2d1c4ed2412c4b3b3927aa2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f2562db9c3e02ae76169c36b086c3f6"><td class="memTemplParams" colspan="2">template&lt;typename genTypeT , typename genTypeU &gt; </td></tr>
<tr class="memitem:ga7f2562db9c3e02ae76169c36b086c3f6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genTypeT&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00323.html#ga7f2562db9c3e02ae76169c36b086c3f6">fastPow</a> (genTypeT x, genTypeU y)</td></tr>
<tr class="memdesc:ga7f2562db9c3e02ae76169c36b086c3f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common pow function but less accurate.  <a href="a00323.html#ga7f2562db9c3e02ae76169c36b086c3f6">More...</a><br /></td></tr>
<tr class="separator:ga7f2562db9c3e02ae76169c36b086c3f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1abe488c0829da5b9de70ac64aeaa7e5"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1abe488c0829da5b9de70ac64aeaa7e5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00323.html#ga1abe488c0829da5b9de70ac64aeaa7e5">fastPow</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga1abe488c0829da5b9de70ac64aeaa7e5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Faster than the common pow function but less accurate.  <a href="a00323.html#ga1abe488c0829da5b9de70ac64aeaa7e5">More...</a><br /></td></tr>
<tr class="separator:ga1abe488c0829da5b9de70ac64aeaa7e5"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00031.html" title="GLM_GTX_fast_exponential ">glm/gtx/fast_exponential.hpp</a>&gt; to use the features of this extension. </p>
<p>Fast but less accurate implementations of exponential based functions. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaa3180ac8f96ab37ab96e0cacaf608e10"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fastExp </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common exp function but less accurate. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00323.html" title="Include <glm/gtx/fast_exponential.hpp> to use the features of this extension. ">GLM_GTX_fast_exponential</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3ba6153aec6bd74628f8b00530aa8d58"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fastExp </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common exp function but less accurate. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00323.html" title="Include <glm/gtx/fast_exponential.hpp> to use the features of this extension. ">GLM_GTX_fast_exponential</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0af50585955eb14c60bb286297fabab2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fastExp2 </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common exp2 function but less accurate. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00323.html" title="Include <glm/gtx/fast_exponential.hpp> to use the features of this extension. ">GLM_GTX_fast_exponential</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacaaed8b67d20d244b7de217e7816c1b6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fastExp2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common exp2 function but less accurate. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00323.html" title="Include <glm/gtx/fast_exponential.hpp> to use the features of this extension. ">GLM_GTX_fast_exponential</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae1bdc97b7f96a600e29c753f1cd4388a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fastLog </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common log function but less accurate. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00323.html" title="Include <glm/gtx/fast_exponential.hpp> to use the features of this extension. ">GLM_GTX_fast_exponential</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga937256993a7219e73f186bb348fe6be8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fastLog </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common exp2 function but less accurate. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00323.html" title="Include <glm/gtx/fast_exponential.hpp> to use the features of this extension. ">GLM_GTX_fast_exponential</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6e98118685f6dc9e05fbb13dd5e5234e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::fastLog2 </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common log2 function but less accurate. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00323.html" title="Include <glm/gtx/fast_exponential.hpp> to use the features of this extension. ">GLM_GTX_fast_exponential</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7562043539194ccc24649f8475bc5584"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fastLog2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common log2 function but less accurate. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00323.html" title="Include <glm/gtx/fast_exponential.hpp> to use the features of this extension. ">GLM_GTX_fast_exponential</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5340e98a11fcbbd936ba6e983a154d50"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::fastPow </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common pow function but less accurate. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00323.html" title="Include <glm/gtx/fast_exponential.hpp> to use the features of this extension. ">GLM_GTX_fast_exponential</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga15325a8ed2d1c4ed2412c4b3b3927aa2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fastPow </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common pow function but less accurate. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00323.html" title="Include <glm/gtx/fast_exponential.hpp> to use the features of this extension. ">GLM_GTX_fast_exponential</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7f2562db9c3e02ae76169c36b086c3f6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genTypeT glm::fastPow </td>
          <td>(</td>
          <td class="paramtype">genTypeT&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genTypeU&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common pow function but less accurate. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00323.html" title="Include <glm/gtx/fast_exponential.hpp> to use the features of this extension. ">GLM_GTX_fast_exponential</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1abe488c0829da5b9de70ac64aeaa7e5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fastPow </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Faster than the common pow function but less accurate. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00323.html" title="Include <glm/gtx/fast_exponential.hpp> to use the features of this extension. ">GLM_GTX_fast_exponential</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
