<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Matrix types</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">Matrix types<div class="ingroups"><a class="el" href="a00280.html">Core features</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Matrix types of with C columns and R rows where C and R are values between 2 to 4 included.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga21dbd1f987775d7cc7607c139531c7e6"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga21dbd1f987775d7cc7607c139531c7e6">dmat2</a></td></tr>
<tr class="memdesc:ga21dbd1f987775d7cc7607c139531c7e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of double-precision floating-point numbers.  <a href="a00283.html#ga21dbd1f987775d7cc7607c139531c7e6">More...</a><br /></td></tr>
<tr class="separator:ga21dbd1f987775d7cc7607c139531c7e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga66b6a9af787e468a46dfe24189e87f9b"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga66b6a9af787e468a46dfe24189e87f9b">dmat2x2</a></td></tr>
<tr class="memdesc:ga66b6a9af787e468a46dfe24189e87f9b"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of double-precision floating-point numbers.  <a href="a00283.html#ga66b6a9af787e468a46dfe24189e87f9b">More...</a><br /></td></tr>
<tr class="separator:ga66b6a9af787e468a46dfe24189e87f9b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga92cd388753d48e20de69ea2dbedf826a"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga92cd388753d48e20de69ea2dbedf826a">dmat2x3</a></td></tr>
<tr class="memdesc:ga92cd388753d48e20de69ea2dbedf826a"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 3 components matrix of double-precision floating-point numbers.  <a href="a00283.html#ga92cd388753d48e20de69ea2dbedf826a">More...</a><br /></td></tr>
<tr class="separator:ga92cd388753d48e20de69ea2dbedf826a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaef2198807e937072803ae0ae45e1965e"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#gaef2198807e937072803ae0ae45e1965e">dmat2x4</a></td></tr>
<tr class="memdesc:gaef2198807e937072803ae0ae45e1965e"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 4 components matrix of double-precision floating-point numbers.  <a href="a00283.html#gaef2198807e937072803ae0ae45e1965e">More...</a><br /></td></tr>
<tr class="separator:gaef2198807e937072803ae0ae45e1965e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6f40aa56265b4b0ccad41b86802efe33"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga6f40aa56265b4b0ccad41b86802efe33">dmat3</a></td></tr>
<tr class="memdesc:ga6f40aa56265b4b0ccad41b86802efe33"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of double-precision floating-point numbers.  <a href="a00283.html#ga6f40aa56265b4b0ccad41b86802efe33">More...</a><br /></td></tr>
<tr class="separator:ga6f40aa56265b4b0ccad41b86802efe33"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga001e3e0638fbf8719788fc64c5b8cf39"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga001e3e0638fbf8719788fc64c5b8cf39">dmat3x2</a></td></tr>
<tr class="memdesc:ga001e3e0638fbf8719788fc64c5b8cf39"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 2 components matrix of double-precision floating-point numbers.  <a href="a00283.html#ga001e3e0638fbf8719788fc64c5b8cf39">More...</a><br /></td></tr>
<tr class="separator:ga001e3e0638fbf8719788fc64c5b8cf39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga970cb3306be25a5ca5db5a9456831228"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga970cb3306be25a5ca5db5a9456831228">dmat3x3</a></td></tr>
<tr class="memdesc:ga970cb3306be25a5ca5db5a9456831228"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of double-precision floating-point numbers.  <a href="a00283.html#ga970cb3306be25a5ca5db5a9456831228">More...</a><br /></td></tr>
<tr class="separator:ga970cb3306be25a5ca5db5a9456831228"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0412a634d183587e6188e9b11869f8f4"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga0412a634d183587e6188e9b11869f8f4">dmat3x4</a></td></tr>
<tr class="memdesc:ga0412a634d183587e6188e9b11869f8f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 4 components matrix of double-precision floating-point numbers.  <a href="a00283.html#ga0412a634d183587e6188e9b11869f8f4">More...</a><br /></td></tr>
<tr class="separator:ga0412a634d183587e6188e9b11869f8f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0f34486bb7fec8e5a5b3830b6a6cbeca"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga0f34486bb7fec8e5a5b3830b6a6cbeca">dmat4</a></td></tr>
<tr class="memdesc:ga0f34486bb7fec8e5a5b3830b6a6cbeca"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of double-precision floating-point numbers.  <a href="a00283.html#ga0f34486bb7fec8e5a5b3830b6a6cbeca">More...</a><br /></td></tr>
<tr class="separator:ga0f34486bb7fec8e5a5b3830b6a6cbeca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9bc0b3ab8b6ba2cb6782e179ad7ad156"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga9bc0b3ab8b6ba2cb6782e179ad7ad156">dmat4x2</a></td></tr>
<tr class="memdesc:ga9bc0b3ab8b6ba2cb6782e179ad7ad156"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 2 components matrix of double-precision floating-point numbers.  <a href="a00283.html#ga9bc0b3ab8b6ba2cb6782e179ad7ad156">More...</a><br /></td></tr>
<tr class="separator:ga9bc0b3ab8b6ba2cb6782e179ad7ad156"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacd18864049f8c83799babe7e596ca05b"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#gacd18864049f8c83799babe7e596ca05b">dmat4x3</a></td></tr>
<tr class="memdesc:gacd18864049f8c83799babe7e596ca05b"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 3 components matrix of double-precision floating-point numbers.  <a href="a00283.html#gacd18864049f8c83799babe7e596ca05b">More...</a><br /></td></tr>
<tr class="separator:gacd18864049f8c83799babe7e596ca05b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad5a6484b983b74f9d801cab8bc4e6a10"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, double, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#gad5a6484b983b74f9d801cab8bc4e6a10">dmat4x4</a></td></tr>
<tr class="memdesc:gad5a6484b983b74f9d801cab8bc4e6a10"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of double-precision floating-point numbers.  <a href="a00283.html#gad5a6484b983b74f9d801cab8bc4e6a10">More...</a><br /></td></tr>
<tr class="separator:gad5a6484b983b74f9d801cab8bc4e6a10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8dd59e7fc6913ac5d61b86553e9148ba"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga8dd59e7fc6913ac5d61b86553e9148ba">mat2</a></td></tr>
<tr class="memdesc:ga8dd59e7fc6913ac5d61b86553e9148ba"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of single-precision floating-point numbers.  <a href="a00283.html#ga8dd59e7fc6913ac5d61b86553e9148ba">More...</a><br /></td></tr>
<tr class="separator:ga8dd59e7fc6913ac5d61b86553e9148ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaa17ef6bfa4e4f2692348b1460c8efcb"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#gaaa17ef6bfa4e4f2692348b1460c8efcb">mat2x2</a></td></tr>
<tr class="memdesc:gaaa17ef6bfa4e4f2692348b1460c8efcb"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 2 components matrix of single-precision floating-point numbers.  <a href="a00283.html#gaaa17ef6bfa4e4f2692348b1460c8efcb">More...</a><br /></td></tr>
<tr class="separator:gaaa17ef6bfa4e4f2692348b1460c8efcb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga493ab21243abe564b3f7d381e677d29a"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga493ab21243abe564b3f7d381e677d29a">mat2x3</a></td></tr>
<tr class="memdesc:ga493ab21243abe564b3f7d381e677d29a"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 3 components matrix of single-precision floating-point numbers.  <a href="a00283.html#ga493ab21243abe564b3f7d381e677d29a">More...</a><br /></td></tr>
<tr class="separator:ga493ab21243abe564b3f7d381e677d29a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8e879b57ddd81e5bf5a88929844e8b40"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga8e879b57ddd81e5bf5a88929844e8b40">mat2x4</a></td></tr>
<tr class="memdesc:ga8e879b57ddd81e5bf5a88929844e8b40"><td class="mdescLeft">&#160;</td><td class="mdescRight">2 columns of 4 components matrix of single-precision floating-point numbers.  <a href="a00283.html#ga8e879b57ddd81e5bf5a88929844e8b40">More...</a><br /></td></tr>
<tr class="separator:ga8e879b57ddd81e5bf5a88929844e8b40"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaefb0fc7a4960b782c18708bb6b655262"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#gaefb0fc7a4960b782c18708bb6b655262">mat3</a></td></tr>
<tr class="memdesc:gaefb0fc7a4960b782c18708bb6b655262"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of single-precision floating-point numbers.  <a href="a00283.html#gaefb0fc7a4960b782c18708bb6b655262">More...</a><br /></td></tr>
<tr class="separator:gaefb0fc7a4960b782c18708bb6b655262"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab91887d7565059dac640e3a1921c914a"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#gab91887d7565059dac640e3a1921c914a">mat3x3</a></td></tr>
<tr class="memdesc:gab91887d7565059dac640e3a1921c914a"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 3 components matrix of single-precision floating-point numbers.  <a href="a00283.html#gab91887d7565059dac640e3a1921c914a">More...</a><br /></td></tr>
<tr class="separator:gab91887d7565059dac640e3a1921c914a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf991cad0b34f64e33af186326dbc4d66"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#gaf991cad0b34f64e33af186326dbc4d66">mat3x4</a></td></tr>
<tr class="memdesc:gaf991cad0b34f64e33af186326dbc4d66"><td class="mdescLeft">&#160;</td><td class="mdescRight">3 columns of 4 components matrix of single-precision floating-point numbers.  <a href="a00283.html#gaf991cad0b34f64e33af186326dbc4d66">More...</a><br /></td></tr>
<tr class="separator:gaf991cad0b34f64e33af186326dbc4d66"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad941c947ad6cdd117a0e8554a4754983"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#gad941c947ad6cdd117a0e8554a4754983">mat4x2</a></td></tr>
<tr class="memdesc:gad941c947ad6cdd117a0e8554a4754983"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 2 components matrix of single-precision floating-point numbers.  <a href="a00283.html#gad941c947ad6cdd117a0e8554a4754983">More...</a><br /></td></tr>
<tr class="separator:gad941c947ad6cdd117a0e8554a4754983"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac7574544bb94777bdbd2eb224eb72fd0"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#gac7574544bb94777bdbd2eb224eb72fd0">mat4x3</a></td></tr>
<tr class="memdesc:gac7574544bb94777bdbd2eb224eb72fd0"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 3 components matrix of single-precision floating-point numbers.  <a href="a00283.html#gac7574544bb94777bdbd2eb224eb72fd0">More...</a><br /></td></tr>
<tr class="separator:gac7574544bb94777bdbd2eb224eb72fd0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab2d35cc2655f44d60958d60a1de34e81"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#gab2d35cc2655f44d60958d60a1de34e81">mat4x4</a></td></tr>
<tr class="memdesc:gab2d35cc2655f44d60958d60a1de34e81"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of single-precision floating-point numbers.  <a href="a00283.html#gab2d35cc2655f44d60958d60a1de34e81">More...</a><br /></td></tr>
<tr class="separator:gab2d35cc2655f44d60958d60a1de34e81"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0db98d836c5549d31cf64ecd043b7af7"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, float, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00283.html#ga0db98d836c5549d31cf64ecd043b7af7">mat4</a></td></tr>
<tr class="memdesc:ga0db98d836c5549d31cf64ecd043b7af7"><td class="mdescLeft">&#160;</td><td class="mdescRight">4 columns of 4 components matrix of single-precision floating-point numbers.  <a href="a00283.html#ga0db98d836c5549d31cf64ecd043b7af7">More...</a><br /></td></tr>
<tr class="separator:ga0db98d836c5549d31cf64ecd043b7af7"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Matrix types of with C columns and R rows where C and R are values between 2 to 4 included. </p>
<p>These types have exhaustive sets of operators. </p>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="ga21dbd1f987775d7cc7607c139531c7e6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, f64, defaultp &gt; dmat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00063_source.html#l00020">20</a> of file <a class="el" href="a00063_source.html">matrix_double2x2.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga66b6a9af787e468a46dfe24189e87f9b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, double, defaultp &gt; dmat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00063_source.html#l00015">15</a> of file <a class="el" href="a00063_source.html">matrix_double2x2.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga92cd388753d48e20de69ea2dbedf826a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 3, double, defaultp &gt; dmat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 3 components matrix of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00065_source.html#l00015">15</a> of file <a class="el" href="a00065_source.html">matrix_double2x3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaef2198807e937072803ae0ae45e1965e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 4, double, defaultp &gt; dmat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 4 components matrix of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00067_source.html#l00015">15</a> of file <a class="el" href="a00067_source.html">matrix_double2x4.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga6f40aa56265b4b0ccad41b86802efe33"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, f64, defaultp &gt; dmat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00071_source.html#l00020">20</a> of file <a class="el" href="a00071_source.html">matrix_double3x3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga001e3e0638fbf8719788fc64c5b8cf39"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 2, double, defaultp &gt; dmat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 2 components matrix of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00069_source.html#l00015">15</a> of file <a class="el" href="a00069_source.html">matrix_double3x2.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga970cb3306be25a5ca5db5a9456831228"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, double, defaultp &gt; dmat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00071_source.html#l00015">15</a> of file <a class="el" href="a00071_source.html">matrix_double3x3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga0412a634d183587e6188e9b11869f8f4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 4, double, defaultp &gt; dmat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 4 components matrix of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00073_source.html#l00015">15</a> of file <a class="el" href="a00073_source.html">matrix_double3x4.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga0f34486bb7fec8e5a5b3830b6a6cbeca"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, f64, defaultp &gt; dmat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00079_source.html#l00020">20</a> of file <a class="el" href="a00079_source.html">matrix_double4x4.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga9bc0b3ab8b6ba2cb6782e179ad7ad156"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 2, double, defaultp &gt; dmat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 2 components matrix of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00075_source.html#l00015">15</a> of file <a class="el" href="a00075_source.html">matrix_double4x2.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gacd18864049f8c83799babe7e596ca05b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 3, double, defaultp &gt; dmat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 3 components matrix of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00077_source.html#l00015">15</a> of file <a class="el" href="a00077_source.html">matrix_double4x3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gad5a6484b983b74f9d801cab8bc4e6a10"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, double, defaultp &gt; dmat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of double-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00079_source.html#l00015">15</a> of file <a class="el" href="a00079_source.html">matrix_double4x4.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga8dd59e7fc6913ac5d61b86553e9148ba"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, f32, defaultp &gt; mat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00082_source.html#l00020">20</a> of file <a class="el" href="a00082_source.html">matrix_float2x2.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaaa17ef6bfa4e4f2692348b1460c8efcb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 2, f32, defaultp &gt; mat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 2 components matrix of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00082_source.html#l00015">15</a> of file <a class="el" href="a00082_source.html">matrix_float2x2.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga493ab21243abe564b3f7d381e677d29a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 3, f32, defaultp &gt; mat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 3 components matrix of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00084_source.html#l00015">15</a> of file <a class="el" href="a00084_source.html">matrix_float2x3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga8e879b57ddd81e5bf5a88929844e8b40"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 2, 4, f32, defaultp &gt; mat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>2 columns of 4 components matrix of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00086_source.html#l00015">15</a> of file <a class="el" href="a00086_source.html">matrix_float2x4.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaefb0fc7a4960b782c18708bb6b655262"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, f32, defaultp &gt; mat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00090_source.html#l00020">20</a> of file <a class="el" href="a00090_source.html">matrix_float3x3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gab91887d7565059dac640e3a1921c914a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 3, f32, defaultp &gt; mat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 3 components matrix of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00090_source.html#l00015">15</a> of file <a class="el" href="a00090_source.html">matrix_float3x3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaf991cad0b34f64e33af186326dbc4d66"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 3, 4, f32, defaultp &gt; mat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>3 columns of 4 components matrix of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00092_source.html#l00015">15</a> of file <a class="el" href="a00092_source.html">matrix_float3x4.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga0db98d836c5549d31cf64ecd043b7af7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, f32, defaultp &gt; mat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00098_source.html#l00020">20</a> of file <a class="el" href="a00098_source.html">matrix_float4x4.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gad941c947ad6cdd117a0e8554a4754983"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 2, f32, defaultp &gt; mat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 2 components matrix of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00094_source.html#l00015">15</a> of file <a class="el" href="a00094_source.html">matrix_float4x2.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gac7574544bb94777bdbd2eb224eb72fd0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 3, f32, defaultp &gt; mat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 3 components matrix of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00096_source.html#l00015">15</a> of file <a class="el" href="a00096_source.html">matrix_float4x3.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gab2d35cc2655f44d60958d60a1de34e81"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt; 4, 4, f32, defaultp &gt; mat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>4 columns of 4 components matrix of single-precision floating-point numbers. </p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 4.1.6 Matrices</a> </dd></dl>

<p>Definition at line <a class="el" href="a00098_source.html#l00015">15</a> of file <a class="el" href="a00098_source.html">matrix_float4x4.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
