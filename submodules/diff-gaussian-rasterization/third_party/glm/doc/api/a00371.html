<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Matrix functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Matrix functions<div class="ingroups"><a class="el" href="a00280.html">Core features</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides GLSL matrix functions.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gad7928795124768e058f99dce270f5c8d"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad7928795124768e058f99dce270f5c8d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00371.html#gad7928795124768e058f99dce270f5c8d">determinant</a> (mat&lt; C, R, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:gad7928795124768e058f99dce270f5c8d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the determinant of a squared matrix.  <a href="a00371.html#gad7928795124768e058f99dce270f5c8d">More...</a><br /></td></tr>
<tr class="separator:gad7928795124768e058f99dce270f5c8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaed509fe8129b01e4f20a6d0de5690091"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaed509fe8129b01e4f20a6d0de5690091"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00371.html#gaed509fe8129b01e4f20a6d0de5690091">inverse</a> (mat&lt; C, R, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:gaed509fe8129b01e4f20a6d0de5690091"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the inverse of a squared matrix.  <a href="a00371.html#gaed509fe8129b01e4f20a6d0de5690091">More...</a><br /></td></tr>
<tr class="separator:gaed509fe8129b01e4f20a6d0de5690091"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf14569404c779fedca98d0b9b8e58c1f"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf14569404c779fedca98d0b9b8e58c1f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00371.html#gaf14569404c779fedca98d0b9b8e58c1f">matrixCompMult</a> (mat&lt; C, R, T, Q &gt; const &amp;x, mat&lt; C, R, T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:gaf14569404c779fedca98d0b9b8e58c1f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Multiply matrix x by matrix y component-wise, i.e., result[i][j] is the scalar product of x[i][j] and y[i][j].  <a href="a00371.html#gaf14569404c779fedca98d0b9b8e58c1f">More...</a><br /></td></tr>
<tr class="separator:gaf14569404c779fedca98d0b9b8e58c1f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac29fb7bae75a8e4c1b74cbbf85520e50"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac29fb7bae75a8e4c1b74cbbf85520e50"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL detail::outerProduct_trait&lt; C, R, T, Q &gt;::type&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00371.html#gac29fb7bae75a8e4c1b74cbbf85520e50">outerProduct</a> (vec&lt; C, T, Q &gt; const &amp;c, vec&lt; R, T, Q &gt; const &amp;r)</td></tr>
<tr class="memdesc:gac29fb7bae75a8e4c1b74cbbf85520e50"><td class="mdescLeft">&#160;</td><td class="mdescRight">Treats the first parameter c as a column vector and the second parameter r as a row vector and does a linear algebraic matrix multiply c * r.  <a href="a00371.html#gac29fb7bae75a8e4c1b74cbbf85520e50">More...</a><br /></td></tr>
<tr class="separator:gac29fb7bae75a8e4c1b74cbbf85520e50"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae679d841da8ce9dbcc6c2d454f15bc35"><td class="memTemplParams" colspan="2">template&lt;length_t C, length_t R, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae679d841da8ce9dbcc6c2d454f15bc35"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; C, R, T, Q &gt;::transpose_type&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00371.html#gae679d841da8ce9dbcc6c2d454f15bc35">transpose</a> (mat&lt; C, R, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gae679d841da8ce9dbcc6c2d454f15bc35"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the transposed matrix of x.  <a href="a00371.html#gae679d841da8ce9dbcc6c2d454f15bc35">More...</a><br /></td></tr>
<tr class="separator:gae679d841da8ce9dbcc6c2d454f15bc35"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Provides GLSL matrix functions. </p>
<p>Include &lt;<a class="el" href="a00057.html" title="Core features ">glm/matrix.hpp</a>&gt; to use these core features. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gad7928795124768e058f99dce270f5c8d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::determinant </td>
          <td>(</td>
          <td class="paramtype">mat&lt; C, R, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the determinant of a squared matrix. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">C</td><td>Integer between 1 and 4 included that qualify the number a column </td></tr>
    <tr><td class="paramname">R</td><td>Integer between 1 and 4 included that qualify the number a row </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or signed integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/determinant.xml">GLSL determinant man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.6 Matrix Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaed509fe8129b01e4f20a6d0de5690091"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;C, R, T, Q&gt; glm::inverse </td>
          <td>(</td>
          <td class="paramtype">mat&lt; C, R, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the inverse of a squared matrix. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">C</td><td>Integer between 1 and 4 included that qualify the number a column </td></tr>
    <tr><td class="paramname">R</td><td>Integer between 1 and 4 included that qualify the number a row </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or signed integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/inverse.xml">GLSL inverse man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.6 Matrix Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf14569404c779fedca98d0b9b8e58c1f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;C, R, T, Q&gt; glm::matrixCompMult </td>
          <td>(</td>
          <td class="paramtype">mat&lt; C, R, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; C, R, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Multiply matrix x by matrix y component-wise, i.e., result[i][j] is the scalar product of x[i][j] and y[i][j]. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">C</td><td>Integer between 1 and 4 included that qualify the number a column </td></tr>
    <tr><td class="paramname">R</td><td>Integer between 1 and 4 included that qualify the number a row </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or signed integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/matrixCompMult.xml">GLSL matrixCompMult man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.6 Matrix Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac29fb7bae75a8e4c1b74cbbf85520e50"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL detail::outerProduct_trait&lt;C, R, T, Q&gt;::type glm::outerProduct </td>
          <td>(</td>
          <td class="paramtype">vec&lt; C, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>c</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; R, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>r</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Treats the first parameter c as a column vector and the second parameter r as a row vector and does a linear algebraic matrix multiply c * r. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">C</td><td>Integer between 1 and 4 included that qualify the number a column </td></tr>
    <tr><td class="paramname">R</td><td>Integer between 1 and 4 included that qualify the number a row </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or signed integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/outerProduct.xml">GLSL outerProduct man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.6 Matrix Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae679d841da8ce9dbcc6c2d454f15bc35"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;C, R, T, Q&gt;::transpose_type glm::transpose </td>
          <td>(</td>
          <td class="paramtype">mat&lt; C, R, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the transposed matrix of x. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">C</td><td>Integer between 1 and 4 included that qualify the number a column </td></tr>
    <tr><td class="paramname">R</td><td>Integer between 1 and 4 included that qualify the number a row </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point or signed integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/transpose.xml">GLSL transpose man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.6 Matrix Functions</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
