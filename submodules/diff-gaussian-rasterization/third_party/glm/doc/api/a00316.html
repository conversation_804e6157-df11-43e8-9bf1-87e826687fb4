<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_component_wise</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_component_wise<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00018.html" title="GLM_GTX_component_wise ">glm/gtx/component_wise.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaf71833350e15e74d31cbf8a3e7f27051"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaf71833350e15e74d31cbf8a3e7f27051"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType::value_type&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00316.html#gaf71833350e15e74d31cbf8a3e7f27051">compAdd</a> (genType const &amp;v)</td></tr>
<tr class="memdesc:gaf71833350e15e74d31cbf8a3e7f27051"><td class="mdescLeft">&#160;</td><td class="mdescRight">Add all vector components together.  <a href="a00316.html#gaf71833350e15e74d31cbf8a3e7f27051">More...</a><br /></td></tr>
<tr class="separator:gaf71833350e15e74d31cbf8a3e7f27051"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabfa4bb19298c8c73d4217ba759c496b6"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gabfa4bb19298c8c73d4217ba759c496b6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType::value_type&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00316.html#gabfa4bb19298c8c73d4217ba759c496b6">compMax</a> (genType const &amp;v)</td></tr>
<tr class="memdesc:gabfa4bb19298c8c73d4217ba759c496b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Find the maximum value between single vector components.  <a href="a00316.html#gabfa4bb19298c8c73d4217ba759c496b6">More...</a><br /></td></tr>
<tr class="separator:gabfa4bb19298c8c73d4217ba759c496b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab5d0832b5c7bb01b8d7395973bfb1425"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gab5d0832b5c7bb01b8d7395973bfb1425"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType::value_type&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00316.html#gab5d0832b5c7bb01b8d7395973bfb1425">compMin</a> (genType const &amp;v)</td></tr>
<tr class="memdesc:gab5d0832b5c7bb01b8d7395973bfb1425"><td class="mdescLeft">&#160;</td><td class="mdescRight">Find the minimum value between single vector components.  <a href="a00316.html#gab5d0832b5c7bb01b8d7395973bfb1425">More...</a><br /></td></tr>
<tr class="separator:gab5d0832b5c7bb01b8d7395973bfb1425"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae8ab88024197202c9479d33bdc5a8a5d"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gae8ab88024197202c9479d33bdc5a8a5d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType::value_type&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00316.html#gae8ab88024197202c9479d33bdc5a8a5d">compMul</a> (genType const &amp;v)</td></tr>
<tr class="memdesc:gae8ab88024197202c9479d33bdc5a8a5d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Multiply all vector components together.  <a href="a00316.html#gae8ab88024197202c9479d33bdc5a8a5d">More...</a><br /></td></tr>
<tr class="separator:gae8ab88024197202c9479d33bdc5a8a5d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8f2b81ada8515875e58cb1667b6b9908"><td class="memTemplParams" colspan="2">template&lt;typename floatType , length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8f2b81ada8515875e58cb1667b6b9908"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, floatType, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00316.html#ga8f2b81ada8515875e58cb1667b6b9908">compNormalize</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga8f2b81ada8515875e58cb1667b6b9908"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert an integer vector to a normalized float vector.  <a href="a00316.html#ga8f2b81ada8515875e58cb1667b6b9908">More...</a><br /></td></tr>
<tr class="separator:ga8f2b81ada8515875e58cb1667b6b9908"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga80abc2980d65d675f435d178c36880eb"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , typename floatType , qualifier Q&gt; </td></tr>
<tr class="memitem:ga80abc2980d65d675f435d178c36880eb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00316.html#ga80abc2980d65d675f435d178c36880eb">compScale</a> (vec&lt; L, floatType, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga80abc2980d65d675f435d178c36880eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a normalized float vector to an integer vector.  <a href="a00316.html#ga80abc2980d65d675f435d178c36880eb">More...</a><br /></td></tr>
<tr class="separator:ga80abc2980d65d675f435d178c36880eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00018.html" title="GLM_GTX_component_wise ">glm/gtx/component_wise.hpp</a>&gt; to use the features of this extension. </p>
<p>Operations between components of a type </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaf71833350e15e74d31cbf8a3e7f27051"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType::value_type glm::compAdd </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Add all vector components together. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00316.html" title="Include <glm/gtx/component_wise.hpp> to use the features of this extension. ">GLM_GTX_component_wise</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gabfa4bb19298c8c73d4217ba759c496b6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType::value_type glm::compMax </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Find the maximum value between single vector components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00316.html" title="Include <glm/gtx/component_wise.hpp> to use the features of this extension. ">GLM_GTX_component_wise</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab5d0832b5c7bb01b8d7395973bfb1425"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType::value_type glm::compMin </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Find the minimum value between single vector components. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00316.html" title="Include <glm/gtx/component_wise.hpp> to use the features of this extension. ">GLM_GTX_component_wise</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae8ab88024197202c9479d33bdc5a8a5d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType::value_type glm::compMul </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Multiply all vector components together. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00316.html" title="Include <glm/gtx/component_wise.hpp> to use the features of this extension. ">GLM_GTX_component_wise</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8f2b81ada8515875e58cb1667b6b9908"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, floatType, Q&gt; glm::compNormalize </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert an integer vector to a normalized float vector. </p>
<p>If the parameter value type is already a floating qualifier type, the value is passed through. </p><dl class="section see"><dt>See also</dt><dd><a class="el" href="a00316.html" title="Include <glm/gtx/component_wise.hpp> to use the features of this extension. ">GLM_GTX_component_wise</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga80abc2980d65d675f435d178c36880eb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::compScale </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, floatType, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a normalized float vector to an integer vector. </p>
<p>If the parameter value type is already a floating qualifier type, the value is passed through. </p><dl class="section see"><dt>See also</dt><dd><a class="el" href="a00316.html" title="Include <glm/gtx/component_wise.hpp> to use the features of this extension. ">GLM_GTX_component_wise</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
