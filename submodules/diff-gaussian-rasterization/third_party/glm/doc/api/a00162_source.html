<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: type_aligned.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">gtx/type_aligned.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00162.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../gtc/type_precision.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &quot;../gtc/quaternion.hpp&quot;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_type_aligned is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_type_aligned extension included&quot;)</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;{</div>
<div class="line"><a name="l00031"></a><span class="lineno">   31</span>&#160;        <span class="comment">// Signed int vector types</span></div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga760bcf26fdb23a2c3ecad3c928a19ae6">lowp_int8</a>, aligned_lowp_int8, 1);</div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;</div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga698e36b01167fc0f037889334dce8def">lowp_int16</a>, aligned_lowp_int16, 2);</div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;</div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga864aabca5f3296e176e0c3ed9cc16b02">lowp_int32</a>, aligned_lowp_int32, 4);</div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;</div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaf645b1a60203b39c0207baff5e3d8c3c">lowp_int64</a>, aligned_lowp_int64, 8);</div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga119c41d73fe9977358174eb3ac1035a3">lowp_int8_t</a>, aligned_lowp_int8_t, 1);</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div>
<div class="line"><a name="l00059"></a><span class="lineno">   59</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga8b2cd8d31eb345b2d641d9261c38db1a">lowp_int16_t</a>, aligned_lowp_int16_t, 2);</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div>
<div class="line"><a name="l00063"></a><span class="lineno">   63</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga0350631d35ff800e6133ac6243b13cbc">lowp_int32_t</a>, aligned_lowp_int32_t, 4);</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaebf341fc4a5be233f7dde962c2e33847">lowp_int64_t</a>, aligned_lowp_int64_t, 8);</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;</div>
<div class="line"><a name="l00072"></a><span class="lineno">   72</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga552a6bde5e75984efb0f863278da2e54">lowp_i8</a>, aligned_lowp_i8, 1);</div>
<div class="line"><a name="l00073"></a><span class="lineno">   73</span>&#160;</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga392b673fd10847bfb78fb808c6cf8ff7">lowp_i16</a>, aligned_lowp_i16, 2);</div>
<div class="line"><a name="l00077"></a><span class="lineno">   77</span>&#160;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga7ff73a45cea9613ebf1a9fad0b9f82ac">lowp_i32</a>, aligned_lowp_i32, 4);</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga354736e0c645099cd44c42fb2f87c2b8">lowp_i64</a>, aligned_lowp_i64, 8);</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga6fbd69cbdaa44345bff923a2cf63de7e">mediump_int8</a>, aligned_mediump_int8, 1);</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;</div>
<div class="line"><a name="l00093"></a><span class="lineno">   93</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gadff3608baa4b5bd3ed28f95c1c2c345d">mediump_int16</a>, aligned_mediump_int16, 2);</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;</div>
<div class="line"><a name="l00097"></a><span class="lineno">   97</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga5244cef85d6e870e240c76428a262ae8">mediump_int32</a>, aligned_mediump_int32, 4);</div>
<div class="line"><a name="l00098"></a><span class="lineno">   98</span>&#160;</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga7b968f2b86a0442a89c7359171e1d866">mediump_int64</a>, aligned_mediump_int64, 8);</div>
<div class="line"><a name="l00102"></a><span class="lineno">  102</span>&#160;</div>
<div class="line"><a name="l00103"></a><span class="lineno">  103</span>&#160;</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga6d7b3789ecb932c26430009478cac7ae">mediump_int8_t</a>, aligned_mediump_int8_t, 1);</div>
<div class="line"><a name="l00107"></a><span class="lineno">  107</span>&#160;</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga80e72fe94c88498537e8158ba7591c54">mediump_int16_t</a>, aligned_mediump_int16_t, 2);</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga26fc7ced1ad7ca5024f1c973c8dc9180">mediump_int32_t</a>, aligned_mediump_int32_t, 4);</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;</div>
<div class="line"><a name="l00118"></a><span class="lineno">  118</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gac3bc41bcac61d1ba8f02a6f68ce23f64">mediump_int64_t</a>, aligned_mediump_int64_t, 8);</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;</div>
<div class="line"><a name="l00123"></a><span class="lineno">  123</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gacf1ded173e1e2d049c511d095b259e21">mediump_i8</a>, aligned_mediump_i8, 1);</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;</div>
<div class="line"><a name="l00127"></a><span class="lineno">  127</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga62a17cddeb4dffb4e18fe3aea23f051a">mediump_i16</a>, aligned_mediump_i16, 2);</div>
<div class="line"><a name="l00128"></a><span class="lineno">  128</span>&#160;</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaf5e94bf2a20af7601787c154751dc2e1">mediump_i32</a>, aligned_mediump_i32, 4);</div>
<div class="line"><a name="l00132"></a><span class="lineno">  132</span>&#160;</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga3ebcb1f6d8d8387253de8bccb058d77f">mediump_i64</a>, aligned_mediump_i64, 8);</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div>
<div class="line"><a name="l00137"></a><span class="lineno">  137</span>&#160;</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gad0549c902a96a7164e4ac858d5f39dbf">highp_int8</a>, aligned_highp_int8, 1);</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga5fde0fa4a3852a9dd5d637a92ee74718">highp_int16</a>, aligned_highp_int16, 2);</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;</div>
<div class="line"><a name="l00148"></a><span class="lineno">  148</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga84ed04b4e0de18c977e932d617e7c223">highp_int32</a>, aligned_highp_int32, 4);</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;</div>
<div class="line"><a name="l00152"></a><span class="lineno">  152</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga226a8d52b4e3f77aaa6231135e886aac">highp_int64</a>, aligned_highp_int64, 8);</div>
<div class="line"><a name="l00153"></a><span class="lineno">  153</span>&#160;</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;</div>
<div class="line"><a name="l00157"></a><span class="lineno">  157</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga1085c50dd8fbeb5e7e609b1c127492a5">highp_int8_t</a>, aligned_highp_int8_t, 1);</div>
<div class="line"><a name="l00158"></a><span class="lineno">  158</span>&#160;</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gacaea06d0a79ef3172e887a7a6ba434ff">highp_int16_t</a>, aligned_highp_int16_t, 2);</div>
<div class="line"><a name="l00162"></a><span class="lineno">  162</span>&#160;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga2c71c8bd9e2fe7d2e93ca250d8b6157f">highp_int32_t</a>, aligned_highp_int32_t, 4);</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga73c6abb280a45feeff60f9accaee91f3">highp_int64_t</a>, aligned_highp_int64_t, 8);</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gacb88796f2d08ef253d0345aff20c3aee">highp_i8</a>, aligned_highp_i8, 1);</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;</div>
<div class="line"><a name="l00178"></a><span class="lineno">  178</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga0336abc2604dd2c20c30e036454b64f8">highp_i16</a>, aligned_highp_i16, 2);</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;</div>
<div class="line"><a name="l00182"></a><span class="lineno">  182</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga727675ac6b5d2fc699520e0059735e25">highp_i32</a>, aligned_highp_i32, 4);</div>
<div class="line"><a name="l00183"></a><span class="lineno">  183</span>&#160;</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gac25db6d2b1e2a0f351b77ba3409ac4cd">highp_i64</a>, aligned_highp_i64, 8);</div>
<div class="line"><a name="l00187"></a><span class="lineno">  187</span>&#160;</div>
<div class="line"><a name="l00188"></a><span class="lineno">  188</span>&#160;</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(int8, aligned_int8, 1);</div>
<div class="line"><a name="l00192"></a><span class="lineno">  192</span>&#160;</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(int16, aligned_int16, 2);</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(int32, aligned_int32, 4);</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;</div>
<div class="line"><a name="l00203"></a><span class="lineno">  203</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>, aligned_int64, 8);</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;</div>
<div class="line"><a name="l00208"></a><span class="lineno">  208</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga4bf09d8838a86866b39ee6e109341645">int8_t</a>, aligned_int8_t, 1);</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;</div>
<div class="line"><a name="l00212"></a><span class="lineno">  212</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gae8f5e3e964ca2ae240adc2c0d74adede">int16_t</a>, aligned_int16_t, 2);</div>
<div class="line"><a name="l00213"></a><span class="lineno">  213</span>&#160;</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga042ef09ff2f0cb24a36f541bcb3a3710">int32_t</a>, aligned_int32_t, 4);</div>
<div class="line"><a name="l00217"></a><span class="lineno">  217</span>&#160;</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga322a7d7d2c2c68994dc872a33de63c61">int64_t</a>, aligned_int64_t, 8);</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div>
<div class="line"><a name="l00222"></a><span class="lineno">  222</span>&#160;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga302ec977b0c0c3ea245b6c9275495355">i8</a>, aligned_i8, 1);</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;</div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga3ab5fe184343d394fb6c2723c3ee3699">i16</a>, aligned_i16, 2);</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;</div>
<div class="line"><a name="l00233"></a><span class="lineno">  233</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga96faea43ac5f875d2d3ffbf8d213e3eb">i32</a>, aligned_i32, 4);</div>
<div class="line"><a name="l00234"></a><span class="lineno">  234</span>&#160;</div>
<div class="line"><a name="l00237"></a><span class="lineno">  237</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gadb997e409103d4da18abd837e636a496">i64</a>, aligned_i64, 8);</div>
<div class="line"><a name="l00238"></a><span class="lineno">  238</span>&#160;</div>
<div class="line"><a name="l00239"></a><span class="lineno">  239</span>&#160;</div>
<div class="line"><a name="l00242"></a><span class="lineno">  242</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00272.html#gaedd0562c2e77714929d7723a7e2e0dba">ivec1</a>, <a class="code" href="a00303.html#ga76298aed82a439063c3d55980c84aa0b">aligned_ivec1</a>, 4);</div>
<div class="line"><a name="l00243"></a><span class="lineno">  243</span>&#160;</div>
<div class="line"><a name="l00246"></a><span class="lineno">  246</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00281.html#ga6f9269106d91b2d2b91bcf27cd5f5560">ivec2</a>, <a class="code" href="a00303.html#gae4f38fd2c86cee6940986197777b3ca4">aligned_ivec2</a>, 8);</div>
<div class="line"><a name="l00247"></a><span class="lineno">  247</span>&#160;</div>
<div class="line"><a name="l00250"></a><span class="lineno">  250</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00281.html#gad0d784d8eee201aca362484d2daee46c">ivec3</a>, <a class="code" href="a00303.html#ga32794322d294e5ace7fed4a61896f270">aligned_ivec3</a>, 16);</div>
<div class="line"><a name="l00251"></a><span class="lineno">  251</span>&#160;</div>
<div class="line"><a name="l00254"></a><span class="lineno">  254</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00281.html#ga5abb4603dae0ce58c595e66d9123d812">ivec4</a>, <a class="code" href="a00303.html#ga7f79eae5927c9033d84617e49f6f34e4">aligned_ivec4</a>, 16);</div>
<div class="line"><a name="l00255"></a><span class="lineno">  255</span>&#160;</div>
<div class="line"><a name="l00256"></a><span class="lineno">  256</span>&#160;</div>
<div class="line"><a name="l00259"></a><span class="lineno">  259</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga7e80d927ff0a3861ced68dfff8a4020b">i8vec1</a>, aligned_i8vec1, 1);</div>
<div class="line"><a name="l00260"></a><span class="lineno">  260</span>&#160;</div>
<div class="line"><a name="l00263"></a><span class="lineno">  263</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gad06935764d78f43f9d542c784c2212ec">i8vec2</a>, aligned_i8vec2, 2);</div>
<div class="line"><a name="l00264"></a><span class="lineno">  264</span>&#160;</div>
<div class="line"><a name="l00267"></a><span class="lineno">  267</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga5a08d36cf7917cd19d081a603d0eae3e">i8vec3</a>, aligned_i8vec3, 4);</div>
<div class="line"><a name="l00268"></a><span class="lineno">  268</span>&#160;</div>
<div class="line"><a name="l00271"></a><span class="lineno">  271</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga4177a44206121dabc8c4ff1c0f544574">i8vec4</a>, aligned_i8vec4, 4);</div>
<div class="line"><a name="l00272"></a><span class="lineno">  272</span>&#160;</div>
<div class="line"><a name="l00273"></a><span class="lineno">  273</span>&#160;</div>
<div class="line"><a name="l00276"></a><span class="lineno">  276</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gafe730798732aa7b0647096a004db1b1c">i16vec1</a>, aligned_i16vec1, 2);</div>
<div class="line"><a name="l00277"></a><span class="lineno">  277</span>&#160;</div>
<div class="line"><a name="l00280"></a><span class="lineno">  280</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga2996630ba7b10535af8e065cf326f761">i16vec2</a>, aligned_i16vec2, 4);</div>
<div class="line"><a name="l00281"></a><span class="lineno">  281</span>&#160;</div>
<div class="line"><a name="l00284"></a><span class="lineno">  284</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gae9c90a867a6026b1f6eab00456f3fb8b">i16vec3</a>, aligned_i16vec3, 8);</div>
<div class="line"><a name="l00285"></a><span class="lineno">  285</span>&#160;</div>
<div class="line"><a name="l00288"></a><span class="lineno">  288</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga550831bfc26d1e0101c1cb3d79938c06">i16vec4</a>, aligned_i16vec4, 8);</div>
<div class="line"><a name="l00289"></a><span class="lineno">  289</span>&#160;</div>
<div class="line"><a name="l00290"></a><span class="lineno">  290</span>&#160;</div>
<div class="line"><a name="l00293"></a><span class="lineno">  293</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga54b8a4e0f5a7203a821bf8e9c1265bcf">i32vec1</a>, aligned_i32vec1, 4);</div>
<div class="line"><a name="l00294"></a><span class="lineno">  294</span>&#160;</div>
<div class="line"><a name="l00297"></a><span class="lineno">  297</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga8b44026374982dcd1e52d22bac99247e">i32vec2</a>, aligned_i32vec2, 8);</div>
<div class="line"><a name="l00298"></a><span class="lineno">  298</span>&#160;</div>
<div class="line"><a name="l00301"></a><span class="lineno">  301</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga7f526b5cccef126a2ebcf9bdd890394e">i32vec3</a>, aligned_i32vec3, 16);</div>
<div class="line"><a name="l00302"></a><span class="lineno">  302</span>&#160;</div>
<div class="line"><a name="l00305"></a><span class="lineno">  305</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga866a05905c49912309ed1fa5f5980e61">i32vec4</a>, aligned_i32vec4, 16);</div>
<div class="line"><a name="l00306"></a><span class="lineno">  306</span>&#160;</div>
<div class="line"><a name="l00307"></a><span class="lineno">  307</span>&#160;</div>
<div class="line"><a name="l00310"></a><span class="lineno">  310</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga2b65767f8b5aed1bd1cf86c541662b50">i64vec1</a>, aligned_i64vec1, 8);</div>
<div class="line"><a name="l00311"></a><span class="lineno">  311</span>&#160;</div>
<div class="line"><a name="l00314"></a><span class="lineno">  314</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga48310188e1d0c616bf8d78c92447523b">i64vec2</a>, aligned_i64vec2, 16);</div>
<div class="line"><a name="l00315"></a><span class="lineno">  315</span>&#160;</div>
<div class="line"><a name="l00318"></a><span class="lineno">  318</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga667948cfe6fb3d6606c750729ec49f77">i64vec3</a>, aligned_i64vec3, 32);</div>
<div class="line"><a name="l00319"></a><span class="lineno">  319</span>&#160;</div>
<div class="line"><a name="l00322"></a><span class="lineno">  322</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaa4e31c3d9de067029efeb161a44b0232">i64vec4</a>, aligned_i64vec4, 32);</div>
<div class="line"><a name="l00323"></a><span class="lineno">  323</span>&#160;</div>
<div class="line"><a name="l00324"></a><span class="lineno">  324</span>&#160;</div>
<div class="line"><a name="l00326"></a><span class="lineno">  326</span>&#160;        <span class="comment">// Unsigned int vector types</span></div>
<div class="line"><a name="l00327"></a><span class="lineno">  327</span>&#160;</div>
<div class="line"><a name="l00330"></a><span class="lineno">  330</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaf49470869e9be2c059629b250619804e">lowp_uint8</a>, aligned_lowp_uint8, 1);</div>
<div class="line"><a name="l00331"></a><span class="lineno">  331</span>&#160;</div>
<div class="line"><a name="l00334"></a><span class="lineno">  334</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gad68bfd9f881856fc863a6ebca0b67f78">lowp_uint16</a>, aligned_lowp_uint16, 2);</div>
<div class="line"><a name="l00335"></a><span class="lineno">  335</span>&#160;</div>
<div class="line"><a name="l00338"></a><span class="lineno">  338</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaa6a5b461bbf5fe20982472aa51896d4b">lowp_uint32</a>, aligned_lowp_uint32, 4);</div>
<div class="line"><a name="l00339"></a><span class="lineno">  339</span>&#160;</div>
<div class="line"><a name="l00342"></a><span class="lineno">  342</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaa212b805736a759998e312cbdd550fae">lowp_uint64</a>, aligned_lowp_uint64, 8);</div>
<div class="line"><a name="l00343"></a><span class="lineno">  343</span>&#160;</div>
<div class="line"><a name="l00344"></a><span class="lineno">  344</span>&#160;</div>
<div class="line"><a name="l00347"></a><span class="lineno">  347</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga667b2ece2b258be898812dc2177995d1">lowp_uint8_t</a>, aligned_lowp_uint8_t, 1);</div>
<div class="line"><a name="l00348"></a><span class="lineno">  348</span>&#160;</div>
<div class="line"><a name="l00351"></a><span class="lineno">  351</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga91c4815f93177eb423362fd296a87e9f">lowp_uint16_t</a>, aligned_lowp_uint16_t, 2);</div>
<div class="line"><a name="l00352"></a><span class="lineno">  352</span>&#160;</div>
<div class="line"><a name="l00355"></a><span class="lineno">  355</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaf1b735b4b1145174f4e4167d13778f9b">lowp_uint32_t</a>, aligned_lowp_uint32_t, 4);</div>
<div class="line"><a name="l00356"></a><span class="lineno">  356</span>&#160;</div>
<div class="line"><a name="l00359"></a><span class="lineno">  359</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga8dd3a3281ae5c970ffe0c41d538aa153">lowp_uint64_t</a>, aligned_lowp_uint64_t, 8);</div>
<div class="line"><a name="l00360"></a><span class="lineno">  360</span>&#160;</div>
<div class="line"><a name="l00361"></a><span class="lineno">  361</span>&#160;</div>
<div class="line"><a name="l00364"></a><span class="lineno">  364</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga1b09f03da7ac43055c68a349d5445083">lowp_u8</a>, aligned_lowp_u8, 1);</div>
<div class="line"><a name="l00365"></a><span class="lineno">  365</span>&#160;</div>
<div class="line"><a name="l00368"></a><span class="lineno">  368</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga504ce1631cb2ac02fcf1d44d8c2aa126">lowp_u16</a>, aligned_lowp_u16, 2);</div>
<div class="line"><a name="l00369"></a><span class="lineno">  369</span>&#160;</div>
<div class="line"><a name="l00372"></a><span class="lineno">  372</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga4f072ada9552e1e480bbb3b1acde5250">lowp_u32</a>, aligned_lowp_u32, 4);</div>
<div class="line"><a name="l00373"></a><span class="lineno">  373</span>&#160;</div>
<div class="line"><a name="l00376"></a><span class="lineno">  376</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga30069d1f02b19599cbfadf98c23ac6ed">lowp_u64</a>, aligned_lowp_u64, 8);</div>
<div class="line"><a name="l00377"></a><span class="lineno">  377</span>&#160;</div>
<div class="line"><a name="l00378"></a><span class="lineno">  378</span>&#160;</div>
<div class="line"><a name="l00381"></a><span class="lineno">  381</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga1fa92a233b9110861cdbc8c2ccf0b5a3">mediump_uint8</a>, aligned_mediump_uint8, 1);</div>
<div class="line"><a name="l00382"></a><span class="lineno">  382</span>&#160;</div>
<div class="line"><a name="l00385"></a><span class="lineno">  385</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga2885a6c89916911e418c06bb76b9bdbb">mediump_uint16</a>, aligned_mediump_uint16, 2);</div>
<div class="line"><a name="l00386"></a><span class="lineno">  386</span>&#160;</div>
<div class="line"><a name="l00389"></a><span class="lineno">  389</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga34dd5ec1988c443bae80f1b20a8ade5f">mediump_uint32</a>, aligned_mediump_uint32, 4);</div>
<div class="line"><a name="l00390"></a><span class="lineno">  390</span>&#160;</div>
<div class="line"><a name="l00393"></a><span class="lineno">  393</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga30652709815ad9404272a31957daa59e">mediump_uint64</a>, aligned_mediump_uint64, 8);</div>
<div class="line"><a name="l00394"></a><span class="lineno">  394</span>&#160;</div>
<div class="line"><a name="l00395"></a><span class="lineno">  395</span>&#160;</div>
<div class="line"><a name="l00398"></a><span class="lineno">  398</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gadfe65c78231039e90507770db50c98c7">mediump_uint8_t</a>, aligned_mediump_uint8_t, 1);</div>
<div class="line"><a name="l00399"></a><span class="lineno">  399</span>&#160;</div>
<div class="line"><a name="l00402"></a><span class="lineno">  402</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga3963b1050fc65a383ee28e3f827b6e3e">mediump_uint16_t</a>, aligned_mediump_uint16_t, 2);</div>
<div class="line"><a name="l00403"></a><span class="lineno">  403</span>&#160;</div>
<div class="line"><a name="l00406"></a><span class="lineno">  406</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaf4dae276fd29623950de14a6ca2586b5">mediump_uint32_t</a>, aligned_mediump_uint32_t, 4);</div>
<div class="line"><a name="l00407"></a><span class="lineno">  407</span>&#160;</div>
<div class="line"><a name="l00410"></a><span class="lineno">  410</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga9b170dd4a8f38448a2dc93987c7875e9">mediump_uint64_t</a>, aligned_mediump_uint64_t, 8);</div>
<div class="line"><a name="l00411"></a><span class="lineno">  411</span>&#160;</div>
<div class="line"><a name="l00412"></a><span class="lineno">  412</span>&#160;</div>
<div class="line"><a name="l00415"></a><span class="lineno">  415</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gad1213a22bbb9e4107f07eaa4956f8281">mediump_u8</a>, aligned_mediump_u8, 1);</div>
<div class="line"><a name="l00416"></a><span class="lineno">  416</span>&#160;</div>
<div class="line"><a name="l00419"></a><span class="lineno">  419</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga9df98857be695d5a30cb30f5bfa38a80">mediump_u16</a>, aligned_mediump_u16, 2);</div>
<div class="line"><a name="l00420"></a><span class="lineno">  420</span>&#160;</div>
<div class="line"><a name="l00423"></a><span class="lineno">  423</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga1bd0e914158bf03135f8a317de6debe9">mediump_u32</a>, aligned_mediump_u32, 4);</div>
<div class="line"><a name="l00424"></a><span class="lineno">  424</span>&#160;</div>
<div class="line"><a name="l00427"></a><span class="lineno">  427</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga2af9490085ae3bdf36a544e9dd073610">mediump_u64</a>, aligned_mediump_u64, 8);</div>
<div class="line"><a name="l00428"></a><span class="lineno">  428</span>&#160;</div>
<div class="line"><a name="l00429"></a><span class="lineno">  429</span>&#160;</div>
<div class="line"><a name="l00432"></a><span class="lineno">  432</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga97432f9979e73e66567361fd01e4cffb">highp_uint8</a>, aligned_highp_uint8, 1);</div>
<div class="line"><a name="l00433"></a><span class="lineno">  433</span>&#160;</div>
<div class="line"><a name="l00436"></a><span class="lineno">  436</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga746dc6da204f5622e395f492997dbf57">highp_uint16</a>, aligned_highp_uint16, 2);</div>
<div class="line"><a name="l00437"></a><span class="lineno">  437</span>&#160;</div>
<div class="line"><a name="l00440"></a><span class="lineno">  440</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga256b12b650c3f2fb86878fd1c5db8bc3">highp_uint32</a>, aligned_highp_uint32, 4);</div>
<div class="line"><a name="l00441"></a><span class="lineno">  441</span>&#160;</div>
<div class="line"><a name="l00444"></a><span class="lineno">  444</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaa38d732f5d4a7bc42a1b43b9d3c141ce">highp_uint64</a>, aligned_highp_uint64, 8);</div>
<div class="line"><a name="l00445"></a><span class="lineno">  445</span>&#160;</div>
<div class="line"><a name="l00446"></a><span class="lineno">  446</span>&#160;</div>
<div class="line"><a name="l00449"></a><span class="lineno">  449</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gac4e00a26a2adb5f2c0a7096810df29e5">highp_uint8_t</a>, aligned_highp_uint8_t, 1);</div>
<div class="line"><a name="l00450"></a><span class="lineno">  450</span>&#160;</div>
<div class="line"><a name="l00453"></a><span class="lineno">  453</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gacf54c3330ef60aa3d16cb676c7bcb8c7">highp_uint16_t</a>, aligned_highp_uint16_t, 2);</div>
<div class="line"><a name="l00454"></a><span class="lineno">  454</span>&#160;</div>
<div class="line"><a name="l00457"></a><span class="lineno">  457</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gae978599c9711ac263ba732d4ac225b0e">highp_uint32_t</a>, aligned_highp_uint32_t, 4);</div>
<div class="line"><a name="l00458"></a><span class="lineno">  458</span>&#160;</div>
<div class="line"><a name="l00461"></a><span class="lineno">  461</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaa46172d7dc1c7ffe3e78107ff88adf08">highp_uint64_t</a>, aligned_highp_uint64_t, 8);</div>
<div class="line"><a name="l00462"></a><span class="lineno">  462</span>&#160;</div>
<div class="line"><a name="l00463"></a><span class="lineno">  463</span>&#160;</div>
<div class="line"><a name="l00466"></a><span class="lineno">  466</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gacd1259f3a9e8d2a9df5be2d74322ef9c">highp_u8</a>, aligned_highp_u8, 1);</div>
<div class="line"><a name="l00467"></a><span class="lineno">  467</span>&#160;</div>
<div class="line"><a name="l00470"></a><span class="lineno">  470</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga8e62c883d13f47015f3b70ed88751369">highp_u16</a>, aligned_highp_u16, 2);</div>
<div class="line"><a name="l00471"></a><span class="lineno">  471</span>&#160;</div>
<div class="line"><a name="l00474"></a><span class="lineno">  474</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga7a6f1929464dcc680b16381a4ee5f2cf">highp_u32</a>, aligned_highp_u32, 4);</div>
<div class="line"><a name="l00475"></a><span class="lineno">  475</span>&#160;</div>
<div class="line"><a name="l00478"></a><span class="lineno">  478</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga0c181fdf06a309691999926b6690c969">highp_u64</a>, aligned_highp_u64, 8);</div>
<div class="line"><a name="l00479"></a><span class="lineno">  479</span>&#160;</div>
<div class="line"><a name="l00480"></a><span class="lineno">  480</span>&#160;</div>
<div class="line"><a name="l00483"></a><span class="lineno">  483</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(uint8, aligned_uint8, 1);</div>
<div class="line"><a name="l00484"></a><span class="lineno">  484</span>&#160;</div>
<div class="line"><a name="l00487"></a><span class="lineno">  487</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(uint16, aligned_uint16, 2);</div>
<div class="line"><a name="l00488"></a><span class="lineno">  488</span>&#160;</div>
<div class="line"><a name="l00491"></a><span class="lineno">  491</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(uint32, aligned_uint32, 4);</div>
<div class="line"><a name="l00492"></a><span class="lineno">  492</span>&#160;</div>
<div class="line"><a name="l00495"></a><span class="lineno">  495</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>, aligned_uint64, 8);</div>
<div class="line"><a name="l00496"></a><span class="lineno">  496</span>&#160;</div>
<div class="line"><a name="l00497"></a><span class="lineno">  497</span>&#160;</div>
<div class="line"><a name="l00500"></a><span class="lineno">  500</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga28d97808322d3c92186e4a0c067d7e8e">uint8_t</a>, aligned_uint8_t, 1);</div>
<div class="line"><a name="l00501"></a><span class="lineno">  501</span>&#160;</div>
<div class="line"><a name="l00504"></a><span class="lineno">  504</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga91f91f411080c37730856ff5887f5bcf">uint16_t</a>, aligned_uint16_t, 2);</div>
<div class="line"><a name="l00505"></a><span class="lineno">  505</span>&#160;</div>
<div class="line"><a name="l00508"></a><span class="lineno">  508</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga2171d9dc1fefb1c82e2817f45b622eac">uint32_t</a>, aligned_uint32_t, 4);</div>
<div class="line"><a name="l00509"></a><span class="lineno">  509</span>&#160;</div>
<div class="line"><a name="l00512"></a><span class="lineno">  512</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga3999d3e7ff22025c16ddb601e14dfdee">uint64_t</a>, aligned_uint64_t, 8);</div>
<div class="line"><a name="l00513"></a><span class="lineno">  513</span>&#160;</div>
<div class="line"><a name="l00514"></a><span class="lineno">  514</span>&#160;</div>
<div class="line"><a name="l00517"></a><span class="lineno">  517</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaecc7082561fc9028b844b6cf3d305d36">u8</a>, aligned_u8, 1);</div>
<div class="line"><a name="l00518"></a><span class="lineno">  518</span>&#160;</div>
<div class="line"><a name="l00521"></a><span class="lineno">  521</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaa2d7acc0adb536fab71fe261232a40ff">u16</a>, aligned_u16, 2);</div>
<div class="line"><a name="l00522"></a><span class="lineno">  522</span>&#160;</div>
<div class="line"><a name="l00525"></a><span class="lineno">  525</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga8165913e068444f7842302d40ba897b9">u32</a>, aligned_u32, 4);</div>
<div class="line"><a name="l00526"></a><span class="lineno">  526</span>&#160;</div>
<div class="line"><a name="l00529"></a><span class="lineno">  529</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaf3f312156984c365e9f65620354da70b">u64</a>, aligned_u64, 8);</div>
<div class="line"><a name="l00530"></a><span class="lineno">  530</span>&#160;</div>
<div class="line"><a name="l00531"></a><span class="lineno">  531</span>&#160;</div>
<div class="line"><a name="l00534"></a><span class="lineno">  534</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00276.html#gac3bdd96183d23876c58a1424585fefe7">uvec1</a>, <a class="code" href="a00303.html#ga1ff8ed402c93d280ff0597c1c5e7c548">aligned_uvec1</a>, 4);</div>
<div class="line"><a name="l00535"></a><span class="lineno">  535</span>&#160;</div>
<div class="line"><a name="l00538"></a><span class="lineno">  538</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00281.html#ga2f6d9ec3ae14813ade37d6aee3715fdb">uvec2</a>, <a class="code" href="a00303.html#ga074137e3be58528d67041c223d49f398">aligned_uvec2</a>, 8);</div>
<div class="line"><a name="l00539"></a><span class="lineno">  539</span>&#160;</div>
<div class="line"><a name="l00542"></a><span class="lineno">  542</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00281.html#ga3d3e55874babd4bf93baa7bbc83ae418">uvec3</a>, <a class="code" href="a00303.html#ga2a8d9c3046f89d854eb758adfa0811c0">aligned_uvec3</a>, 16);</div>
<div class="line"><a name="l00543"></a><span class="lineno">  543</span>&#160;</div>
<div class="line"><a name="l00546"></a><span class="lineno">  546</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00281.html#gaa57e96bb337867329d5f43bcc27c1095">uvec4</a>, <a class="code" href="a00303.html#gabf842c45eea186170c267a328e3f3b7d">aligned_uvec4</a>, 16);</div>
<div class="line"><a name="l00547"></a><span class="lineno">  547</span>&#160;</div>
<div class="line"><a name="l00548"></a><span class="lineno">  548</span>&#160;</div>
<div class="line"><a name="l00551"></a><span class="lineno">  551</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga29b349e037f0b24320b4548a143daee2">u8vec1</a>, aligned_u8vec1, 1);</div>
<div class="line"><a name="l00552"></a><span class="lineno">  552</span>&#160;</div>
<div class="line"><a name="l00555"></a><span class="lineno">  555</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">u8vec2</a>, aligned_u8vec2, 2);</div>
<div class="line"><a name="l00556"></a><span class="lineno">  556</span>&#160;</div>
<div class="line"><a name="l00559"></a><span class="lineno">  559</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga7c5706f6bbe5282e5598acf7e7b377e2">u8vec3</a>, aligned_u8vec3, 4);</div>
<div class="line"><a name="l00560"></a><span class="lineno">  560</span>&#160;</div>
<div class="line"><a name="l00563"></a><span class="lineno">  563</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga20779a61de2fd526a17f12fe53ec46b1">u8vec4</a>, aligned_u8vec4, 4);</div>
<div class="line"><a name="l00564"></a><span class="lineno">  564</span>&#160;</div>
<div class="line"><a name="l00565"></a><span class="lineno">  565</span>&#160;</div>
<div class="line"><a name="l00568"></a><span class="lineno">  568</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga08c05ba8ffb19f5d14ab584e1e9e9ee5">u16vec1</a>, aligned_u16vec1, 2);</div>
<div class="line"><a name="l00569"></a><span class="lineno">  569</span>&#160;</div>
<div class="line"><a name="l00572"></a><span class="lineno">  572</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">u16vec2</a>, aligned_u16vec2, 4);</div>
<div class="line"><a name="l00573"></a><span class="lineno">  573</span>&#160;</div>
<div class="line"><a name="l00576"></a><span class="lineno">  576</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga1c522ca821c27b862fe51cf4024b064b">u16vec3</a>, aligned_u16vec3, 8);</div>
<div class="line"><a name="l00577"></a><span class="lineno">  577</span>&#160;</div>
<div class="line"><a name="l00580"></a><span class="lineno">  580</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga529496d75775fb656a07993ea9af2450">u16vec4</a>, aligned_u16vec4, 8);</div>
<div class="line"><a name="l00581"></a><span class="lineno">  581</span>&#160;</div>
<div class="line"><a name="l00582"></a><span class="lineno">  582</span>&#160;</div>
<div class="line"><a name="l00585"></a><span class="lineno">  585</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gae627372cfd5f20dd87db490387b71195">u32vec1</a>, aligned_u32vec1, 4);</div>
<div class="line"><a name="l00586"></a><span class="lineno">  586</span>&#160;</div>
<div class="line"><a name="l00589"></a><span class="lineno">  589</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">u32vec2</a>, aligned_u32vec2, 8);</div>
<div class="line"><a name="l00590"></a><span class="lineno">  590</span>&#160;</div>
<div class="line"><a name="l00593"></a><span class="lineno">  593</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gae267358ff2a41d156d97f5762630235a">u32vec3</a>, aligned_u32vec3, 16);</div>
<div class="line"><a name="l00594"></a><span class="lineno">  594</span>&#160;</div>
<div class="line"><a name="l00597"></a><span class="lineno">  597</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga31cef34e4cd04840c54741ff2f7005f0">u32vec4</a>, aligned_u32vec4, 16);</div>
<div class="line"><a name="l00598"></a><span class="lineno">  598</span>&#160;</div>
<div class="line"><a name="l00599"></a><span class="lineno">  599</span>&#160;</div>
<div class="line"><a name="l00602"></a><span class="lineno">  602</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaf09f3ca4b671a4a4f84505eb4cc865fd">u64vec1</a>, aligned_u64vec1, 8);</div>
<div class="line"><a name="l00603"></a><span class="lineno">  603</span>&#160;</div>
<div class="line"><a name="l00606"></a><span class="lineno">  606</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaef3824ed4fe435a019c5b9dddf53fec5">u64vec2</a>, aligned_u64vec2, 16);</div>
<div class="line"><a name="l00607"></a><span class="lineno">  607</span>&#160;</div>
<div class="line"><a name="l00610"></a><span class="lineno">  610</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga489b89ba93d4f7b3934df78debc52276">u64vec3</a>, aligned_u64vec3, 32);</div>
<div class="line"><a name="l00611"></a><span class="lineno">  611</span>&#160;</div>
<div class="line"><a name="l00614"></a><span class="lineno">  614</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga3945dd6515d4498cb603e65ff867ab03">u64vec4</a>, aligned_u64vec4, 32);</div>
<div class="line"><a name="l00615"></a><span class="lineno">  615</span>&#160;</div>
<div class="line"><a name="l00616"></a><span class="lineno">  616</span>&#160;</div>
<div class="line"><a name="l00618"></a><span class="lineno">  618</span>&#160;        <span class="comment">// Float vector types</span></div>
<div class="line"><a name="l00619"></a><span class="lineno">  619</span>&#160;</div>
<div class="line"><a name="l00622"></a><span class="lineno">  622</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaacdc525d6f7bddb3ae95d5c311bd06a1">float32</a>, aligned_float32, 4);</div>
<div class="line"><a name="l00623"></a><span class="lineno">  623</span>&#160;</div>
<div class="line"><a name="l00626"></a><span class="lineno">  626</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaa4947bc8b47c72fceea9bda730ecf603">float32_t</a>, aligned_float32_t, 4);</div>
<div class="line"><a name="l00627"></a><span class="lineno">  627</span>&#160;</div>
<div class="line"><a name="l00630"></a><span class="lineno">  630</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaacdc525d6f7bddb3ae95d5c311bd06a1">float32</a>, aligned_f32, 4);</div>
<div class="line"><a name="l00631"></a><span class="lineno">  631</span>&#160;</div>
<div class="line"><a name="l00632"></a><span class="lineno">  632</span>&#160;<span class="preprocessor">#       ifndef GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l00633"></a><span class="lineno">  633</span>&#160;</div>
<div class="line"><a name="l00636"></a><span class="lineno">  636</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga232fad1b0d6dcc7c16aabde98b2e2a80">float64</a>, aligned_float64, 8);</div>
<div class="line"><a name="l00637"></a><span class="lineno">  637</span>&#160;</div>
<div class="line"><a name="l00640"></a><span class="lineno">  640</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga728366fef72cd96f0a5fa6429f05469e">float64_t</a>, aligned_float64_t, 8);</div>
<div class="line"><a name="l00641"></a><span class="lineno">  641</span>&#160;</div>
<div class="line"><a name="l00644"></a><span class="lineno">  644</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga232fad1b0d6dcc7c16aabde98b2e2a80">float64</a>, aligned_f64, 8);</div>
<div class="line"><a name="l00645"></a><span class="lineno">  645</span>&#160;</div>
<div class="line"><a name="l00646"></a><span class="lineno">  646</span>&#160;<span class="preprocessor">#       endif//GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l00647"></a><span class="lineno">  647</span>&#160;</div>
<div class="line"><a name="l00648"></a><span class="lineno">  648</span>&#160;</div>
<div class="line"><a name="l00651"></a><span class="lineno">  651</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00270.html#gadfc071d934d8dae7955a1d530a3cf656">vec1</a>, <a class="code" href="a00303.html#ga05e6d4c908965d04191c2070a8d0a65e">aligned_vec1</a>, 4);</div>
<div class="line"><a name="l00652"></a><span class="lineno">  652</span>&#160;</div>
<div class="line"><a name="l00655"></a><span class="lineno">  655</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a>, <a class="code" href="a00303.html#ga0682462f8096a226773e20fac993cde5">aligned_vec2</a>, 8);</div>
<div class="line"><a name="l00656"></a><span class="lineno">  656</span>&#160;</div>
<div class="line"><a name="l00659"></a><span class="lineno">  659</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a>, <a class="code" href="a00303.html#ga7cf643b66664e0cd3c48759ae66c2bd0">aligned_vec3</a>, 16);</div>
<div class="line"><a name="l00660"></a><span class="lineno">  660</span>&#160;</div>
<div class="line"><a name="l00663"></a><span class="lineno">  663</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a>, <a class="code" href="a00303.html#ga85d89e83cb8137e1be1446de8c3b643a">aligned_vec4</a>, 16);</div>
<div class="line"><a name="l00664"></a><span class="lineno">  664</span>&#160;</div>
<div class="line"><a name="l00665"></a><span class="lineno">  665</span>&#160;</div>
<div class="line"><a name="l00668"></a><span class="lineno">  668</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga98b9ed43cf8c5cf1d354b23c7df9119f">fvec1</a>, aligned_fvec1, 4);</div>
<div class="line"><a name="l00669"></a><span class="lineno">  669</span>&#160;</div>
<div class="line"><a name="l00672"></a><span class="lineno">  672</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga24273aa02abaecaab7f160bac437a339">fvec2</a>, aligned_fvec2, 8);</div>
<div class="line"><a name="l00673"></a><span class="lineno">  673</span>&#160;</div>
<div class="line"><a name="l00676"></a><span class="lineno">  676</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga89930533646b30d021759298aa6bf04a">fvec3</a>, aligned_fvec3, 16);</div>
<div class="line"><a name="l00677"></a><span class="lineno">  677</span>&#160;</div>
<div class="line"><a name="l00680"></a><span class="lineno">  680</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga713c796c54875cf4092d42ff9d9096b0">fvec4</a>, aligned_fvec4, 16);</div>
<div class="line"><a name="l00681"></a><span class="lineno">  681</span>&#160;</div>
<div class="line"><a name="l00682"></a><span class="lineno">  682</span>&#160;</div>
<div class="line"><a name="l00685"></a><span class="lineno">  685</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga701f32ab5b3fb06996b41f5c0d643805">f32vec1</a>, aligned_f32vec1, 4);</div>
<div class="line"><a name="l00686"></a><span class="lineno">  686</span>&#160;</div>
<div class="line"><a name="l00689"></a><span class="lineno">  689</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga5d6c70e080409a76a257dc55bd8ea2c8">f32vec2</a>, aligned_f32vec2, 8);</div>
<div class="line"><a name="l00690"></a><span class="lineno">  690</span>&#160;</div>
<div class="line"><a name="l00693"></a><span class="lineno">  693</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaea5c4518e175162e306d2c2b5ef5ac79">f32vec3</a>, aligned_f32vec3, 16);</div>
<div class="line"><a name="l00694"></a><span class="lineno">  694</span>&#160;</div>
<div class="line"><a name="l00697"></a><span class="lineno">  697</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga31c6ca0e074a44007f49a9a3720b18c8">f32vec4</a>, aligned_f32vec4, 16);</div>
<div class="line"><a name="l00698"></a><span class="lineno">  698</span>&#160;</div>
<div class="line"><a name="l00699"></a><span class="lineno">  699</span>&#160;</div>
<div class="line"><a name="l00702"></a><span class="lineno">  702</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00268.html#ga6221af17edc2d4477a4583d2cd53e569">dvec1</a>, <a class="code" href="a00303.html#ga4974f46ae5a19415d91316960a53617a">aligned_dvec1</a>, 8);</div>
<div class="line"><a name="l00703"></a><span class="lineno">  703</span>&#160;</div>
<div class="line"><a name="l00706"></a><span class="lineno">  706</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00281.html#ga8b09c71aaac7da7867ae58377fe219a8">dvec2</a>, <a class="code" href="a00303.html#ga18d859f87122b2b3b2992ffe86dbebc0">aligned_dvec2</a>, 16);</div>
<div class="line"><a name="l00707"></a><span class="lineno">  707</span>&#160;</div>
<div class="line"><a name="l00710"></a><span class="lineno">  710</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00281.html#ga5b83ae3d0fdec519c038e4d2cf967cf0">dvec3</a>, <a class="code" href="a00303.html#gaa37869eea77d28419b2fb0ff70b69bf0">aligned_dvec3</a>, 32);</div>
<div class="line"><a name="l00711"></a><span class="lineno">  711</span>&#160;</div>
<div class="line"><a name="l00714"></a><span class="lineno">  714</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00281.html#ga57debab5d98ce618f7b2a97fe26eb3ac">dvec4</a>, <a class="code" href="a00303.html#ga8a9f0a4795ccc442fa9901845026f9f5">aligned_dvec4</a>, 32);</div>
<div class="line"><a name="l00715"></a><span class="lineno">  715</span>&#160;</div>
<div class="line"><a name="l00716"></a><span class="lineno">  716</span>&#160;</div>
<div class="line"><a name="l00717"></a><span class="lineno">  717</span>&#160;<span class="preprocessor">#       ifndef GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l00718"></a><span class="lineno">  718</span>&#160;</div>
<div class="line"><a name="l00721"></a><span class="lineno">  721</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gade502df1ce14f837fae7f60a03ddb9b0">f64vec1</a>, aligned_f64vec1, 8);</div>
<div class="line"><a name="l00722"></a><span class="lineno">  722</span>&#160;</div>
<div class="line"><a name="l00725"></a><span class="lineno">  725</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gadc4e1594f9555d919131ee02b17822a2">f64vec2</a>, aligned_f64vec2, 16);</div>
<div class="line"><a name="l00726"></a><span class="lineno">  726</span>&#160;</div>
<div class="line"><a name="l00729"></a><span class="lineno">  729</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaa7a1ddca75c5f629173bf4772db7a635">f64vec3</a>, aligned_f64vec3, 32);</div>
<div class="line"><a name="l00730"></a><span class="lineno">  730</span>&#160;</div>
<div class="line"><a name="l00733"></a><span class="lineno">  733</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga66e92e57260bdb910609b9a56bf83e97">f64vec4</a>, aligned_f64vec4, 32);</div>
<div class="line"><a name="l00734"></a><span class="lineno">  734</span>&#160;</div>
<div class="line"><a name="l00735"></a><span class="lineno">  735</span>&#160;<span class="preprocessor">#       endif//GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l00736"></a><span class="lineno">  736</span>&#160;</div>
<div class="line"><a name="l00738"></a><span class="lineno">  738</span>&#160;        <span class="comment">// Float matrix types</span></div>
<div class="line"><a name="l00739"></a><span class="lineno">  739</span>&#160;</div>
<div class="line"><a name="l00742"></a><span class="lineno">  742</span>&#160;        <span class="comment">//typedef detail::tmat1&lt;f32&gt; mat1;</span></div>
<div class="line"><a name="l00743"></a><span class="lineno">  743</span>&#160;</div>
<div class="line"><a name="l00746"></a><span class="lineno">  746</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00283.html#ga8dd59e7fc6913ac5d61b86553e9148ba">mat2</a>, <a class="code" href="a00303.html#ga5a8a5f8c47cd7d5502dd9932f83472b9">aligned_mat2</a>, 16);</div>
<div class="line"><a name="l00747"></a><span class="lineno">  747</span>&#160;</div>
<div class="line"><a name="l00750"></a><span class="lineno">  750</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00283.html#gaefb0fc7a4960b782c18708bb6b655262">mat3</a>, <a class="code" href="a00303.html#ga43a92a24ca863e0e0f3b65834b3cf714">aligned_mat3</a>, 16);</div>
<div class="line"><a name="l00751"></a><span class="lineno">  751</span>&#160;</div>
<div class="line"><a name="l00754"></a><span class="lineno">  754</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00283.html#ga0db98d836c5549d31cf64ecd043b7af7">mat4</a>, <a class="code" href="a00303.html#gab0fddcf95dd51cbcbf624ea7c40dfeb8">aligned_mat4</a>, 16);</div>
<div class="line"><a name="l00755"></a><span class="lineno">  755</span>&#160;</div>
<div class="line"><a name="l00756"></a><span class="lineno">  756</span>&#160;</div>
<div class="line"><a name="l00759"></a><span class="lineno">  759</span>&#160;        <span class="comment">//typedef detail::tmat1x1&lt;f32&gt; mat1;</span></div>
<div class="line"><a name="l00760"></a><span class="lineno">  760</span>&#160;</div>
<div class="line"><a name="l00763"></a><span class="lineno">  763</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00283.html#gaaa17ef6bfa4e4f2692348b1460c8efcb">mat2x2</a>, <a class="code" href="a00303.html#gabb04f459d81d753d278b2072e2375e8e">aligned_mat2x2</a>, 16);</div>
<div class="line"><a name="l00764"></a><span class="lineno">  764</span>&#160;</div>
<div class="line"><a name="l00767"></a><span class="lineno">  767</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00283.html#gab91887d7565059dac640e3a1921c914a">mat3x3</a>, <a class="code" href="a00303.html#gadb065dbe5c11271fef8cf2ea8608f187">aligned_mat3x3</a>, 16);</div>
<div class="line"><a name="l00768"></a><span class="lineno">  768</span>&#160;</div>
<div class="line"><a name="l00771"></a><span class="lineno">  771</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00283.html#gab2d35cc2655f44d60958d60a1de34e81">mat4x4</a>, <a class="code" href="a00303.html#ga8b8fb86973a0b768c5bd802c92fac1a1">aligned_mat4x4</a>, 16);</div>
<div class="line"><a name="l00772"></a><span class="lineno">  772</span>&#160;</div>
<div class="line"><a name="l00773"></a><span class="lineno">  773</span>&#160;</div>
<div class="line"><a name="l00776"></a><span class="lineno">  776</span>&#160;        <span class="comment">//typedef detail::tmat1x1&lt;f32&gt; fmat1;</span></div>
<div class="line"><a name="l00777"></a><span class="lineno">  777</span>&#160;</div>
<div class="line"><a name="l00780"></a><span class="lineno">  780</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga3350c93c3275298f940a42875388e4b4">fmat2x2</a>, aligned_fmat2, 16);</div>
<div class="line"><a name="l00781"></a><span class="lineno">  781</span>&#160;</div>
<div class="line"><a name="l00784"></a><span class="lineno">  784</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaa07c86650253672a19dbfb898f3265b8">fmat3x3</a>, aligned_fmat3, 16);</div>
<div class="line"><a name="l00785"></a><span class="lineno">  785</span>&#160;</div>
<div class="line"><a name="l00788"></a><span class="lineno">  788</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gad01cc6479bde1fd1870f13d3ed9530b3">fmat4x4</a>, aligned_fmat4, 16);</div>
<div class="line"><a name="l00789"></a><span class="lineno">  789</span>&#160;</div>
<div class="line"><a name="l00790"></a><span class="lineno">  790</span>&#160;</div>
<div class="line"><a name="l00793"></a><span class="lineno">  793</span>&#160;        <span class="comment">//typedef f32 fmat1x1;</span></div>
<div class="line"><a name="l00794"></a><span class="lineno">  794</span>&#160;</div>
<div class="line"><a name="l00797"></a><span class="lineno">  797</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga3350c93c3275298f940a42875388e4b4">fmat2x2</a>, aligned_fmat2x2, 16);</div>
<div class="line"><a name="l00798"></a><span class="lineno">  798</span>&#160;</div>
<div class="line"><a name="l00801"></a><span class="lineno">  801</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga55a2d2a8eb09b5633668257eb3cad453">fmat2x3</a>, aligned_fmat2x3, 16);</div>
<div class="line"><a name="l00802"></a><span class="lineno">  802</span>&#160;</div>
<div class="line"><a name="l00805"></a><span class="lineno">  805</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga681381f19f11c9e5ee45cda2c56937ff">fmat2x4</a>, aligned_fmat2x4, 16);</div>
<div class="line"><a name="l00806"></a><span class="lineno">  806</span>&#160;</div>
<div class="line"><a name="l00809"></a><span class="lineno">  809</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga6af54d70d9beb0a7ef992a879e86b04f">fmat3x2</a>, aligned_fmat3x2, 16);</div>
<div class="line"><a name="l00810"></a><span class="lineno">  810</span>&#160;</div>
<div class="line"><a name="l00813"></a><span class="lineno">  813</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaa07c86650253672a19dbfb898f3265b8">fmat3x3</a>, aligned_fmat3x3, 16);</div>
<div class="line"><a name="l00814"></a><span class="lineno">  814</span>&#160;</div>
<div class="line"><a name="l00817"></a><span class="lineno">  817</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga44e158af77a670ee1b58c03cda9e1619">fmat3x4</a>, aligned_fmat3x4, 16);</div>
<div class="line"><a name="l00818"></a><span class="lineno">  818</span>&#160;</div>
<div class="line"><a name="l00821"></a><span class="lineno">  821</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga8c8aa45aafcc23238edb1d5aeb801774">fmat4x2</a>, aligned_fmat4x2, 16);</div>
<div class="line"><a name="l00822"></a><span class="lineno">  822</span>&#160;</div>
<div class="line"><a name="l00825"></a><span class="lineno">  825</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga4295048a78bdf46b8a7de77ec665b497">fmat4x3</a>, aligned_fmat4x3, 16);</div>
<div class="line"><a name="l00826"></a><span class="lineno">  826</span>&#160;</div>
<div class="line"><a name="l00829"></a><span class="lineno">  829</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gad01cc6479bde1fd1870f13d3ed9530b3">fmat4x4</a>, aligned_fmat4x4, 16);</div>
<div class="line"><a name="l00830"></a><span class="lineno">  830</span>&#160;</div>
<div class="line"><a name="l00831"></a><span class="lineno">  831</span>&#160;</div>
<div class="line"><a name="l00834"></a><span class="lineno">  834</span>&#160;        <span class="comment">//typedef detail::tmat1x1&lt;f32, defaultp&gt; f32mat1;</span></div>
<div class="line"><a name="l00835"></a><span class="lineno">  835</span>&#160;</div>
<div class="line"><a name="l00838"></a><span class="lineno">  838</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga04100c76f7d55a0dd0983ccf05142bff">f32mat2x2</a>, aligned_f32mat2, 16);</div>
<div class="line"><a name="l00839"></a><span class="lineno">  839</span>&#160;</div>
<div class="line"><a name="l00842"></a><span class="lineno">  842</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga65261fa8a21045c8646ddff114a56174">f32mat3x3</a>, aligned_f32mat3, 16);</div>
<div class="line"><a name="l00843"></a><span class="lineno">  843</span>&#160;</div>
<div class="line"><a name="l00846"></a><span class="lineno">  846</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaee4da0e9fbd8cfa2f89cb80889719dc3">f32mat4x4</a>, aligned_f32mat4, 16);</div>
<div class="line"><a name="l00847"></a><span class="lineno">  847</span>&#160;</div>
<div class="line"><a name="l00848"></a><span class="lineno">  848</span>&#160;</div>
<div class="line"><a name="l00851"></a><span class="lineno">  851</span>&#160;        <span class="comment">//typedef f32 f32mat1x1;</span></div>
<div class="line"><a name="l00852"></a><span class="lineno">  852</span>&#160;</div>
<div class="line"><a name="l00855"></a><span class="lineno">  855</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga04100c76f7d55a0dd0983ccf05142bff">f32mat2x2</a>, aligned_f32mat2x2, 16);</div>
<div class="line"><a name="l00856"></a><span class="lineno">  856</span>&#160;</div>
<div class="line"><a name="l00859"></a><span class="lineno">  859</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gab256cdab5eb582e426d749ae77b5b566">f32mat2x3</a>, aligned_f32mat2x3, 16);</div>
<div class="line"><a name="l00860"></a><span class="lineno">  860</span>&#160;</div>
<div class="line"><a name="l00863"></a><span class="lineno">  863</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaf512b74c4400b68f9fdf9388b3d6aac8">f32mat2x4</a>, aligned_f32mat2x4, 16);</div>
<div class="line"><a name="l00864"></a><span class="lineno">  864</span>&#160;</div>
<div class="line"><a name="l00867"></a><span class="lineno">  867</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga1320a08e14fdff3821241eefab6947e9">f32mat3x2</a>, aligned_f32mat3x2, 16);</div>
<div class="line"><a name="l00868"></a><span class="lineno">  868</span>&#160;</div>
<div class="line"><a name="l00871"></a><span class="lineno">  871</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga65261fa8a21045c8646ddff114a56174">f32mat3x3</a>, aligned_f32mat3x3, 16);</div>
<div class="line"><a name="l00872"></a><span class="lineno">  872</span>&#160;</div>
<div class="line"><a name="l00875"></a><span class="lineno">  875</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gab90ade28222f8b861d5ceaf81a3a7f5d">f32mat3x4</a>, aligned_f32mat3x4, 16);</div>
<div class="line"><a name="l00876"></a><span class="lineno">  876</span>&#160;</div>
<div class="line"><a name="l00879"></a><span class="lineno">  879</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga3b32ca1e57a4ef91babbc3d35a34ea20">f32mat4x2</a>, aligned_f32mat4x2, 16);</div>
<div class="line"><a name="l00880"></a><span class="lineno">  880</span>&#160;</div>
<div class="line"><a name="l00883"></a><span class="lineno">  883</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga239b96198771b7add8eea7e6b59840c0">f32mat4x3</a>, aligned_f32mat4x3, 16);</div>
<div class="line"><a name="l00884"></a><span class="lineno">  884</span>&#160;</div>
<div class="line"><a name="l00887"></a><span class="lineno">  887</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaee4da0e9fbd8cfa2f89cb80889719dc3">f32mat4x4</a>, aligned_f32mat4x4, 16);</div>
<div class="line"><a name="l00888"></a><span class="lineno">  888</span>&#160;</div>
<div class="line"><a name="l00889"></a><span class="lineno">  889</span>&#160;</div>
<div class="line"><a name="l00890"></a><span class="lineno">  890</span>&#160;<span class="preprocessor">#       ifndef GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l00891"></a><span class="lineno">  891</span>&#160;</div>
<div class="line"><a name="l00894"></a><span class="lineno">  894</span>&#160;        <span class="comment">//typedef detail::tmat1x1&lt;f64, defaultp&gt; f64mat1;</span></div>
<div class="line"><a name="l00895"></a><span class="lineno">  895</span>&#160;</div>
<div class="line"><a name="l00898"></a><span class="lineno">  898</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga9ec7c4c79e303c053e30729a95fb2c37">f64mat2x2</a>, aligned_f64mat2, 32);</div>
<div class="line"><a name="l00899"></a><span class="lineno">  899</span>&#160;</div>
<div class="line"><a name="l00902"></a><span class="lineno">  902</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga916c1aed91cf91f7b41399ebe7c6e185">f64mat3x3</a>, aligned_f64mat3, 32);</div>
<div class="line"><a name="l00903"></a><span class="lineno">  903</span>&#160;</div>
<div class="line"><a name="l00906"></a><span class="lineno">  906</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gae52e2b7077a9ff928a06ab5ce600b81e">f64mat4x4</a>, aligned_f64mat4, 32);</div>
<div class="line"><a name="l00907"></a><span class="lineno">  907</span>&#160;</div>
<div class="line"><a name="l00908"></a><span class="lineno">  908</span>&#160;</div>
<div class="line"><a name="l00911"></a><span class="lineno">  911</span>&#160;        <span class="comment">//typedef f64 f64mat1x1;</span></div>
<div class="line"><a name="l00912"></a><span class="lineno">  912</span>&#160;</div>
<div class="line"><a name="l00915"></a><span class="lineno">  915</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga9ec7c4c79e303c053e30729a95fb2c37">f64mat2x2</a>, aligned_f64mat2x2, 32);</div>
<div class="line"><a name="l00916"></a><span class="lineno">  916</span>&#160;</div>
<div class="line"><a name="l00919"></a><span class="lineno">  919</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gae3ab5719fc4c1e966631dbbcba8d412a">f64mat2x3</a>, aligned_f64mat2x3, 32);</div>
<div class="line"><a name="l00920"></a><span class="lineno">  920</span>&#160;</div>
<div class="line"><a name="l00923"></a><span class="lineno">  923</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gac87278e0c702ba8afff76316d4eeb769">f64mat2x4</a>, aligned_f64mat2x4, 32);</div>
<div class="line"><a name="l00924"></a><span class="lineno">  924</span>&#160;</div>
<div class="line"><a name="l00927"></a><span class="lineno">  927</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga2473d8bf3f4abf967c4d0e18175be6f7">f64mat3x2</a>, aligned_f64mat3x2, 32);</div>
<div class="line"><a name="l00928"></a><span class="lineno">  928</span>&#160;</div>
<div class="line"><a name="l00931"></a><span class="lineno">  931</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga916c1aed91cf91f7b41399ebe7c6e185">f64mat3x3</a>, aligned_f64mat3x3, 32);</div>
<div class="line"><a name="l00932"></a><span class="lineno">  932</span>&#160;</div>
<div class="line"><a name="l00935"></a><span class="lineno">  935</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gaab239fa9e35b65a67cbaa6ac082f3675">f64mat3x4</a>, aligned_f64mat3x4, 32);</div>
<div class="line"><a name="l00936"></a><span class="lineno">  936</span>&#160;</div>
<div class="line"><a name="l00939"></a><span class="lineno">  939</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gab7daf79d6bc06a68bea1c6f5e11b5512">f64mat4x2</a>, aligned_f64mat4x2, 32);</div>
<div class="line"><a name="l00940"></a><span class="lineno">  940</span>&#160;</div>
<div class="line"><a name="l00943"></a><span class="lineno">  943</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga3e2e66ffbe341a80bc005ba2b9552110">f64mat4x3</a>, aligned_f64mat4x3, 32);</div>
<div class="line"><a name="l00944"></a><span class="lineno">  944</span>&#160;</div>
<div class="line"><a name="l00947"></a><span class="lineno">  947</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#gae52e2b7077a9ff928a06ab5ce600b81e">f64mat4x4</a>, aligned_f64mat4x4, 32);</div>
<div class="line"><a name="l00948"></a><span class="lineno">  948</span>&#160;</div>
<div class="line"><a name="l00949"></a><span class="lineno">  949</span>&#160;<span class="preprocessor">#       endif//GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l00950"></a><span class="lineno">  950</span>&#160;</div>
<div class="line"><a name="l00951"></a><span class="lineno">  951</span>&#160;</div>
<div class="line"><a name="l00953"></a><span class="lineno">  953</span>&#160;        <span class="comment">// Quaternion types</span></div>
<div class="line"><a name="l00954"></a><span class="lineno">  954</span>&#160;</div>
<div class="line"><a name="l00957"></a><span class="lineno">  957</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00252.html#gab0b441adb4509bc58d2946c2239a8942">quat</a>, aligned_quat, 16);</div>
<div class="line"><a name="l00958"></a><span class="lineno">  958</span>&#160;</div>
<div class="line"><a name="l00961"></a><span class="lineno">  961</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00252.html#gab0b441adb4509bc58d2946c2239a8942">quat</a>, aligned_fquat, 16);</div>
<div class="line"><a name="l00962"></a><span class="lineno">  962</span>&#160;</div>
<div class="line"><a name="l00965"></a><span class="lineno">  965</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00249.html#ga1181459aa5d640a3ea43861b118f3f0b">dquat</a>, aligned_dquat, 32);</div>
<div class="line"><a name="l00966"></a><span class="lineno">  966</span>&#160;</div>
<div class="line"><a name="l00969"></a><span class="lineno">  969</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga38e674196ba411d642be40c47bf33939">f32quat</a>, aligned_f32quat, 16);</div>
<div class="line"><a name="l00970"></a><span class="lineno">  970</span>&#160;</div>
<div class="line"><a name="l00971"></a><span class="lineno">  971</span>&#160;<span class="preprocessor">#       ifndef GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l00972"></a><span class="lineno">  972</span>&#160;</div>
<div class="line"><a name="l00975"></a><span class="lineno">  975</span>&#160;        <a class="code" href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">GLM_ALIGNED_TYPEDEF</a>(<a class="code" href="a00304.html#ga2b114a2f2af0fe1dfeb569c767822940">f64quat</a>, aligned_f64quat, 32);</div>
<div class="line"><a name="l00976"></a><span class="lineno">  976</span>&#160;</div>
<div class="line"><a name="l00977"></a><span class="lineno">  977</span>&#160;<span class="preprocessor">#       endif//GLM_FORCE_SINGLE_ONLY</span></div>
<div class="line"><a name="l00978"></a><span class="lineno">  978</span>&#160;</div>
<div class="line"><a name="l00980"></a><span class="lineno">  980</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00981"></a><span class="lineno">  981</span>&#160;</div>
<div class="line"><a name="l00982"></a><span class="lineno">  982</span>&#160;<span class="preprocessor">#include &quot;type_aligned.inl&quot;</span></div>
<div class="ttc" id="a00283_html_gab2d35cc2655f44d60958d60a1de34e81"><div class="ttname"><a href="a00283.html#gab2d35cc2655f44d60958d60a1de34e81">glm::mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, float, defaultp &gt; mat4x4</div><div class="ttdoc">4 columns of 4 components matrix of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00098_source.html#l00015">matrix_float4x4.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_ga0c181fdf06a309691999926b6690c969"><div class="ttname"><a href="a00304.html#ga0c181fdf06a309691999926b6690c969">glm::highp_u64</a></div><div class="ttdeci">uint64 highp_u64</div><div class="ttdoc">High qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00133">fwd.hpp:133</a></div></div>
<div class="ttc" id="a00304_html_gaea5c4518e175162e306d2c2b5ef5ac79"><div class="ttname"><a href="a00304.html#gaea5c4518e175162e306d2c2b5ef5ac79">glm::f32vec3</a></div><div class="ttdeci">vec&lt; 3, f32, defaultp &gt; f32vec3</div><div class="ttdoc">Single-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00463">fwd.hpp:463</a></div></div>
<div class="ttc" id="a00283_html_gaaa17ef6bfa4e4f2692348b1460c8efcb"><div class="ttname"><a href="a00283.html#gaaa17ef6bfa4e4f2692348b1460c8efcb">glm::mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, float, defaultp &gt; mat2x2</div><div class="ttdoc">2 columns of 2 components matrix of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00082_source.html#l00015">matrix_float2x2.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_gaf4dae276fd29623950de14a6ca2586b5"><div class="ttname"><a href="a00304.html#gaf4dae276fd29623950de14a6ca2586b5">glm::mediump_uint32_t</a></div><div class="ttdeci">uint32 mediump_uint32_t</div><div class="ttdoc">Medium qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00127">fwd.hpp:127</a></div></div>
<div class="ttc" id="a00303_html_ga074137e3be58528d67041c223d49f398"><div class="ttname"><a href="a00303.html#ga074137e3be58528d67041c223d49f398">glm::aligned_uvec2</a></div><div class="ttdeci">aligned_highp_uvec2 aligned_uvec2</div><div class="ttdoc">2 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01257">gtc/type_aligned.hpp:1257</a></div></div>
<div class="ttc" id="a00304_html_gaa212b805736a759998e312cbdd550fae"><div class="ttname"><a href="a00304.html#gaa212b805736a759998e312cbdd550fae">glm::lowp_uint64</a></div><div class="ttdeci">uint64 lowp_uint64</div><div class="ttdoc">Low qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00136">fwd.hpp:136</a></div></div>
<div class="ttc" id="a00304_html_ga701f32ab5b3fb06996b41f5c0d643805"><div class="ttname"><a href="a00304.html#ga701f32ab5b3fb06996b41f5c0d643805">glm::f32vec1</a></div><div class="ttdeci">vec&lt; 1, f32, defaultp &gt; f32vec1</div><div class="ttdoc">Single-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00461">fwd.hpp:461</a></div></div>
<div class="ttc" id="a00304_html_ga1b09f03da7ac43055c68a349d5445083"><div class="ttname"><a href="a00304.html#ga1b09f03da7ac43055c68a349d5445083">glm::lowp_u8</a></div><div class="ttdeci">uint8 lowp_u8</div><div class="ttdoc">Low qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00089">fwd.hpp:89</a></div></div>
<div class="ttc" id="a00304_html_ga8165913e068444f7842302d40ba897b9"><div class="ttname"><a href="a00304.html#ga8165913e068444f7842302d40ba897b9">glm::u32</a></div><div class="ttdeci">uint32 u32</div><div class="ttdoc">Default qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00120">fwd.hpp:120</a></div></div>
<div class="ttc" id="a00304_html_ga54b8a4e0f5a7203a821bf8e9c1265bcf"><div class="ttname"><a href="a00304.html#ga54b8a4e0f5a7203a821bf8e9c1265bcf">glm::i32vec1</a></div><div class="ttdeci">vec&lt; 1, i32, defaultp &gt; i32vec1</div><div class="ttdoc">32 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00277">fwd.hpp:277</a></div></div>
<div class="ttc" id="a00304_html_ga746dc6da204f5622e395f492997dbf57"><div class="ttname"><a href="a00304.html#ga746dc6da204f5622e395f492997dbf57">glm::highp_uint16</a></div><div class="ttdeci">uint16 highp_uint16</div><div class="ttdoc">High qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00110">fwd.hpp:110</a></div></div>
<div class="ttc" id="a00304_html_gaab239fa9e35b65a67cbaa6ac082f3675"><div class="ttname"><a href="a00304.html#gaab239fa9e35b65a67cbaa6ac082f3675">glm::f64mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f64, defaultp &gt; f64mat3x4</div><div class="ttdoc">Double-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00787">fwd.hpp:787</a></div></div>
<div class="ttc" id="a00304_html_gae9c90a867a6026b1f6eab00456f3fb8b"><div class="ttname"><a href="a00304.html#gae9c90a867a6026b1f6eab00456f3fb8b">glm::i16vec3</a></div><div class="ttdeci">vec&lt; 3, i16, defaultp &gt; i16vec3</div><div class="ttdoc">16 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00259">fwd.hpp:259</a></div></div>
<div class="ttc" id="a00304_html_gaf1b735b4b1145174f4e4167d13778f9b"><div class="ttname"><a href="a00304.html#gaf1b735b4b1145174f4e4167d13778f9b">glm::lowp_uint32_t</a></div><div class="ttdeci">uint32 lowp_uint32_t</div><div class="ttdoc">Low qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00126">fwd.hpp:126</a></div></div>
<div class="ttc" id="a00304_html_ga34dd5ec1988c443bae80f1b20a8ade5f"><div class="ttname"><a href="a00304.html#ga34dd5ec1988c443bae80f1b20a8ade5f">glm::mediump_uint32</a></div><div class="ttdeci">uint32 mediump_uint32</div><div class="ttdoc">Medium qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00123">fwd.hpp:123</a></div></div>
<div class="ttc" id="a00304_html_gaa38d732f5d4a7bc42a1b43b9d3c141ce"><div class="ttname"><a href="a00304.html#gaa38d732f5d4a7bc42a1b43b9d3c141ce">glm::highp_uint64</a></div><div class="ttdeci">uint64 highp_uint64</div><div class="ttdoc">High qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00138">fwd.hpp:138</a></div></div>
<div class="ttc" id="a00304_html_gaa6a5b461bbf5fe20982472aa51896d4b"><div class="ttname"><a href="a00304.html#gaa6a5b461bbf5fe20982472aa51896d4b">glm::lowp_uint32</a></div><div class="ttdeci">uint32 lowp_uint32</div><div class="ttdoc">Low qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00122">fwd.hpp:122</a></div></div>
<div class="ttc" id="a00281_html_gabe65c061834f61b4f7cb6037b19006a4"><div class="ttname"><a href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">glm::vec2</a></div><div class="ttdeci">vec&lt; 2, float, defaultp &gt; vec2</div><div class="ttdoc">2 components vector of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00208_source.html#l00015">vector_float2.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_gaa4e31c3d9de067029efeb161a44b0232"><div class="ttname"><a href="a00304.html#gaa4e31c3d9de067029efeb161a44b0232">glm::i64vec4</a></div><div class="ttdeci">vec&lt; 4, i64, defaultp &gt; i64vec4</div><div class="ttdoc">64 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00300">fwd.hpp:300</a></div></div>
<div class="ttc" id="a00304_html_ga1c522ca821c27b862fe51cf4024b064b"><div class="ttname"><a href="a00304.html#ga1c522ca821c27b862fe51cf4024b064b">glm::u16vec3</a></div><div class="ttdeci">vec&lt; 3, u16, defaultp &gt; u16vec3</div><div class="ttdoc">Default qualifier 16 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00361">fwd.hpp:361</a></div></div>
<div class="ttc" id="a00303_html_ga32794322d294e5ace7fed4a61896f270"><div class="ttname"><a href="a00303.html#ga32794322d294e5ace7fed4a61896f270">glm::aligned_ivec3</a></div><div class="ttdeci">aligned_highp_ivec3 aligned_ivec3</div><div class="ttdoc">3 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01222">gtc/type_aligned.hpp:1222</a></div></div>
<div class="ttc" id="a00304_html_gad06935764d78f43f9d542c784c2212ec"><div class="ttname"><a href="a00304.html#gad06935764d78f43f9d542c784c2212ec">glm::i8vec2</a></div><div class="ttdeci">vec&lt; 2, i8, defaultp &gt; i8vec2</div><div class="ttdoc">8 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00238">fwd.hpp:238</a></div></div>
<div class="ttc" id="a00303_html_ga7cf643b66664e0cd3c48759ae66c2bd0"><div class="ttname"><a href="a00303.html#ga7cf643b66664e0cd3c48759ae66c2bd0">glm::aligned_vec3</a></div><div class="ttdeci">aligned_highp_vec3 aligned_vec3</div><div class="ttdoc">3 components vector aligned in memory of single-precision floating-point numbers. ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00946">gtc/type_aligned.hpp:946</a></div></div>
<div class="ttc" id="a00281_html_ga3d3e55874babd4bf93baa7bbc83ae418"><div class="ttname"><a href="a00281.html#ga3d3e55874babd4bf93baa7bbc83ae418">glm::uvec3</a></div><div class="ttdeci">vec&lt; 3, unsigned int, defaultp &gt; uvec3</div><div class="ttdoc">3 components vector of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00230_source.html#l00015">vector_uint3.hpp:15</a></div></div>
<div class="ttc" id="a00303_html_ga2a8d9c3046f89d854eb758adfa0811c0"><div class="ttname"><a href="a00303.html#ga2a8d9c3046f89d854eb758adfa0811c0">glm::aligned_uvec3</a></div><div class="ttdeci">aligned_highp_uvec3 aligned_uvec3</div><div class="ttdoc">3 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01260">gtc/type_aligned.hpp:1260</a></div></div>
<div class="ttc" id="a00304_html_ga226a8d52b4e3f77aaa6231135e886aac"><div class="ttname"><a href="a00304.html#ga226a8d52b4e3f77aaa6231135e886aac">glm::highp_int64</a></div><div class="ttdeci">int64 highp_int64</div><div class="ttdoc">High qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00080">fwd.hpp:80</a></div></div>
<div class="ttc" id="a00304_html_ga8b2cd8d31eb345b2d641d9261c38db1a"><div class="ttname"><a href="a00304.html#ga8b2cd8d31eb345b2d641d9261c38db1a">glm::lowp_int16_t</a></div><div class="ttdeci">int16 lowp_int16_t</div><div class="ttdoc">Low qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00054">fwd.hpp:54</a></div></div>
<div class="ttc" id="a00304_html_ga3b32ca1e57a4ef91babbc3d35a34ea20"><div class="ttname"><a href="a00304.html#ga3b32ca1e57a4ef91babbc3d35a34ea20">glm::f32mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f32, defaultp &gt; f32mat4x2</div><div class="ttdoc">Single-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00702">fwd.hpp:702</a></div></div>
<div class="ttc" id="a00304_html_ga1bd0e914158bf03135f8a317de6debe9"><div class="ttname"><a href="a00304.html#ga1bd0e914158bf03135f8a317de6debe9">glm::mediump_u32</a></div><div class="ttdeci">uint32 mediump_u32</div><div class="ttdoc">Medium qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00118">fwd.hpp:118</a></div></div>
<div class="ttc" id="a00364_html_ga95cc03b8b475993fa50e05e38e203303"><div class="ttname"><a href="a00364.html#ga95cc03b8b475993fa50e05e38e203303">glm::GLM_ALIGNED_TYPEDEF</a></div><div class="ttdeci">GLM_ALIGNED_TYPEDEF(f64quat, aligned_f64quat, 32)</div><div class="ttdoc">Double-qualifier floating-point aligned quaternion. </div></div>
<div class="ttc" id="a00303_html_gaa37869eea77d28419b2fb0ff70b69bf0"><div class="ttname"><a href="a00303.html#gaa37869eea77d28419b2fb0ff70b69bf0">glm::aligned_dvec3</a></div><div class="ttdeci">aligned_highp_dvec3 aligned_dvec3</div><div class="ttdoc">3 components vector aligned in memory of double-precision floating-point numbers. ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01114">gtc/type_aligned.hpp:1114</a></div></div>
<div class="ttc" id="a00303_html_ga4974f46ae5a19415d91316960a53617a"><div class="ttname"><a href="a00303.html#ga4974f46ae5a19415d91316960a53617a">glm::aligned_dvec1</a></div><div class="ttdeci">aligned_highp_dvec1 aligned_dvec1</div><div class="ttdoc">1 component vector aligned in memory of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01108">gtc/type_aligned.hpp:1108</a></div></div>
<div class="ttc" id="a00281_html_gad0d784d8eee201aca362484d2daee46c"><div class="ttname"><a href="a00281.html#gad0d784d8eee201aca362484d2daee46c">glm::ivec3</a></div><div class="ttdeci">vec&lt; 3, int, defaultp &gt; ivec3</div><div class="ttdoc">3 components vector of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00218_source.html#l00015">vector_int3.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_ga489b89ba93d4f7b3934df78debc52276"><div class="ttname"><a href="a00304.html#ga489b89ba93d4f7b3934df78debc52276">glm::u64vec3</a></div><div class="ttdeci">vec&lt; 3, u64, defaultp &gt; u64vec3</div><div class="ttdoc">Default qualifier 64 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00401">fwd.hpp:401</a></div></div>
<div class="ttc" id="a00304_html_gaf49470869e9be2c059629b250619804e"><div class="ttname"><a href="a00304.html#gaf49470869e9be2c059629b250619804e">glm::lowp_uint8</a></div><div class="ttdeci">uint8 lowp_uint8</div><div class="ttdoc">Low qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00094">fwd.hpp:94</a></div></div>
<div class="ttc" id="a00304_html_ga30069d1f02b19599cbfadf98c23ac6ed"><div class="ttname"><a href="a00304.html#ga30069d1f02b19599cbfadf98c23ac6ed">glm::lowp_u64</a></div><div class="ttdeci">uint64 lowp_u64</div><div class="ttdoc">Low qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00131">fwd.hpp:131</a></div></div>
<div class="ttc" id="a00304_html_ga6fbd69cbdaa44345bff923a2cf63de7e"><div class="ttname"><a href="a00304.html#ga6fbd69cbdaa44345bff923a2cf63de7e">glm::mediump_int8</a></div><div class="ttdeci">int8 mediump_int8</div><div class="ttdoc">Medium qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00037">fwd.hpp:37</a></div></div>
<div class="ttc" id="a00304_html_gaf645b1a60203b39c0207baff5e3d8c3c"><div class="ttname"><a href="a00304.html#gaf645b1a60203b39c0207baff5e3d8c3c">glm::lowp_int64</a></div><div class="ttdeci">int64 lowp_int64</div><div class="ttdoc">Low qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00078">fwd.hpp:78</a></div></div>
<div class="ttc" id="a00304_html_gaef3824ed4fe435a019c5b9dddf53fec5"><div class="ttname"><a href="a00304.html#gaef3824ed4fe435a019c5b9dddf53fec5">glm::u64vec2</a></div><div class="ttdeci">vec&lt; 2, u64, defaultp &gt; u64vec2</div><div class="ttdoc">Default qualifier 64 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00400">fwd.hpp:400</a></div></div>
<div class="ttc" id="a00304_html_gab90ade28222f8b861d5ceaf81a3a7f5d"><div class="ttname"><a href="a00304.html#gab90ade28222f8b861d5ceaf81a3a7f5d">glm::f32mat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f32, defaultp &gt; f32mat3x4</div><div class="ttdoc">Single-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00707">fwd.hpp:707</a></div></div>
<div class="ttc" id="a00304_html_gaf3f312156984c365e9f65620354da70b"><div class="ttname"><a href="a00304.html#gaf3f312156984c365e9f65620354da70b">glm::u64</a></div><div class="ttdeci">uint64 u64</div><div class="ttdoc">Default qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00134">fwd.hpp:134</a></div></div>
<div class="ttc" id="a00304_html_gade502df1ce14f837fae7f60a03ddb9b0"><div class="ttname"><a href="a00304.html#gade502df1ce14f837fae7f60a03ddb9b0">glm::f64vec1</a></div><div class="ttdeci">vec&lt; 1, f64, defaultp &gt; f64vec1</div><div class="ttdoc">Double-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00501">fwd.hpp:501</a></div></div>
<div class="ttc" id="a00304_html_gafe730798732aa7b0647096a004db1b1c"><div class="ttname"><a href="a00304.html#gafe730798732aa7b0647096a004db1b1c">glm::i16vec1</a></div><div class="ttdeci">vec&lt; 1, i16, defaultp &gt; i16vec1</div><div class="ttdoc">16 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00257">fwd.hpp:257</a></div></div>
<div class="ttc" id="a00304_html_ga232fad1b0d6dcc7c16aabde98b2e2a80"><div class="ttname"><a href="a00304.html#ga232fad1b0d6dcc7c16aabde98b2e2a80">glm::float64</a></div><div class="ttdeci">double float64</div><div class="ttdoc">Double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00171">fwd.hpp:171</a></div></div>
<div class="ttc" id="a00304_html_ga8c8aa45aafcc23238edb1d5aeb801774"><div class="ttname"><a href="a00304.html#ga8c8aa45aafcc23238edb1d5aeb801774">glm::fmat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f32, defaultp &gt; fmat4x2</div><div class="ttdoc">Single-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00662">fwd.hpp:662</a></div></div>
<div class="ttc" id="a00304_html_ga44e158af77a670ee1b58c03cda9e1619"><div class="ttname"><a href="a00304.html#ga44e158af77a670ee1b58c03cda9e1619">glm::fmat3x4</a></div><div class="ttdeci">mat&lt; 3, 4, f32, defaultp &gt; fmat3x4</div><div class="ttdoc">Single-qualifier floating-point 3x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00667">fwd.hpp:667</a></div></div>
<div class="ttc" id="a00304_html_gaf512b74c4400b68f9fdf9388b3d6aac8"><div class="ttname"><a href="a00304.html#gaf512b74c4400b68f9fdf9388b3d6aac8">glm::f32mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f32, defaultp &gt; f32mat2x4</div><div class="ttdoc">Single-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00706">fwd.hpp:706</a></div></div>
<div class="ttc" id="a00304_html_ga550831bfc26d1e0101c1cb3d79938c06"><div class="ttname"><a href="a00304.html#ga550831bfc26d1e0101c1cb3d79938c06">glm::i16vec4</a></div><div class="ttdeci">vec&lt; 4, i16, defaultp &gt; i16vec4</div><div class="ttdoc">16 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00260">fwd.hpp:260</a></div></div>
<div class="ttc" id="a00304_html_ga667b2ece2b258be898812dc2177995d1"><div class="ttname"><a href="a00304.html#ga667b2ece2b258be898812dc2177995d1">glm::lowp_uint8_t</a></div><div class="ttdeci">uint8 lowp_uint8_t</div><div class="ttdoc">Low qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00098">fwd.hpp:98</a></div></div>
<div class="ttc" id="a00304_html_gae978599c9711ac263ba732d4ac225b0e"><div class="ttname"><a href="a00304.html#gae978599c9711ac263ba732d4ac225b0e">glm::highp_uint32_t</a></div><div class="ttdeci">uint32 highp_uint32_t</div><div class="ttdoc">High qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00128">fwd.hpp:128</a></div></div>
<div class="ttc" id="a00304_html_gaa07c86650253672a19dbfb898f3265b8"><div class="ttname"><a href="a00304.html#gaa07c86650253672a19dbfb898f3265b8">glm::fmat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, defaultp &gt; fmat3x3</div><div class="ttdoc">Single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00664">fwd.hpp:664</a></div></div>
<div class="ttc" id="a00304_html_gab256cdab5eb582e426d749ae77b5b566"><div class="ttname"><a href="a00304.html#gab256cdab5eb582e426d749ae77b5b566">glm::f32mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f32, defaultp &gt; f32mat2x3</div><div class="ttdoc">Single-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00703">fwd.hpp:703</a></div></div>
<div class="ttc" id="a00304_html_gadff3608baa4b5bd3ed28f95c1c2c345d"><div class="ttname"><a href="a00304.html#gadff3608baa4b5bd3ed28f95c1c2c345d">glm::mediump_int16</a></div><div class="ttdeci">int16 mediump_int16</div><div class="ttdoc">Medium qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00051">fwd.hpp:51</a></div></div>
<div class="ttc" id="a00304_html_ga9df98857be695d5a30cb30f5bfa38a80"><div class="ttname"><a href="a00304.html#ga9df98857be695d5a30cb30f5bfa38a80">glm::mediump_u16</a></div><div class="ttdeci">uint16 mediump_u16</div><div class="ttdoc">Medium qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00104">fwd.hpp:104</a></div></div>
<div class="ttc" id="a00304_html_ga2b114a2f2af0fe1dfeb569c767822940"><div class="ttname"><a href="a00304.html#ga2b114a2f2af0fe1dfeb569c767822940">glm::f64quat</a></div><div class="ttdeci">qua&lt; f64, defaultp &gt; f64quat</div><div class="ttdoc">Double-qualifier floating-point quaternion. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00815">fwd.hpp:815</a></div></div>
<div class="ttc" id="a00249_html_ga1181459aa5d640a3ea43861b118f3f0b"><div class="ttname"><a href="a00249.html#ga1181459aa5d640a3ea43861b118f3f0b">glm::dquat</a></div><div class="ttdeci">qua&lt; double, defaultp &gt; dquat</div><div class="ttdoc">Quaternion of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00128_source.html#l00035">quaternion_double.hpp:35</a></div></div>
<div class="ttc" id="a00304_html_gaf09f3ca4b671a4a4f84505eb4cc865fd"><div class="ttname"><a href="a00304.html#gaf09f3ca4b671a4a4f84505eb4cc865fd">glm::u64vec1</a></div><div class="ttdeci">vec&lt; 1, u64, defaultp &gt; u64vec1</div><div class="ttdoc">Default qualifier 64 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00399">fwd.hpp:399</a></div></div>
<div class="ttc" id="a00304_html_ga322a7d7d2c2c68994dc872a33de63c61"><div class="ttname"><a href="a00304.html#ga322a7d7d2c2c68994dc872a33de63c61">glm::int64_t</a></div><div class="ttdeci">int64 int64_t</div><div class="ttdoc">64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00085">fwd.hpp:85</a></div></div>
<div class="ttc" id="a00303_html_ga5a8a5f8c47cd7d5502dd9932f83472b9"><div class="ttname"><a href="a00303.html#ga5a8a5f8c47cd7d5502dd9932f83472b9">glm::aligned_mat2</a></div><div class="ttdeci">aligned_highp_mat2 aligned_mat2</div><div class="ttdoc">2 by 2 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00964">gtc/type_aligned.hpp:964</a></div></div>
<div class="ttc" id="a00304_html_ga29b349e037f0b24320b4548a143daee2"><div class="ttname"><a href="a00304.html#ga29b349e037f0b24320b4548a143daee2">glm::u8vec1</a></div><div class="ttdeci">vec&lt; 1, u8, defaultp &gt; u8vec1</div><div class="ttdoc">Default qualifier 8 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00339">fwd.hpp:339</a></div></div>
<div class="ttc" id="a00304_html_ga20779a61de2fd526a17f12fe53ec46b1"><div class="ttname"><a href="a00304.html#ga20779a61de2fd526a17f12fe53ec46b1">glm::u8vec4</a></div><div class="ttdeci">vec&lt; 4, u8, defaultp &gt; u8vec4</div><div class="ttdoc">Default qualifier 8 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00342">fwd.hpp:342</a></div></div>
<div class="ttc" id="a00304_html_ga4bf09d8838a86866b39ee6e109341645"><div class="ttname"><a href="a00304.html#ga4bf09d8838a86866b39ee6e109341645">glm::int8_t</a></div><div class="ttdeci">int8 int8_t</div><div class="ttdoc">8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00043">fwd.hpp:43</a></div></div>
<div class="ttc" id="a00304_html_ga96faea43ac5f875d2d3ffbf8d213e3eb"><div class="ttname"><a href="a00304.html#ga96faea43ac5f875d2d3ffbf8d213e3eb">glm::i32</a></div><div class="ttdeci">int32 i32</div><div class="ttdoc">32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00062">fwd.hpp:62</a></div></div>
<div class="ttc" id="a00304_html_ga9ec7c4c79e303c053e30729a95fb2c37"><div class="ttname"><a href="a00304.html#ga9ec7c4c79e303c053e30729a95fb2c37">glm::f64mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f64, defaultp &gt; f64mat2x2</div><div class="ttdoc">Double-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00780">fwd.hpp:780</a></div></div>
<div class="ttc" id="a00304_html_ga4177a44206121dabc8c4ff1c0f544574"><div class="ttname"><a href="a00304.html#ga4177a44206121dabc8c4ff1c0f544574">glm::i8vec4</a></div><div class="ttdeci">vec&lt; 4, i8, defaultp &gt; i8vec4</div><div class="ttdoc">8 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00240">fwd.hpp:240</a></div></div>
<div class="ttc" id="a00304_html_ga84ed04b4e0de18c977e932d617e7c223"><div class="ttname"><a href="a00304.html#ga84ed04b4e0de18c977e932d617e7c223">glm::highp_int32</a></div><div class="ttdeci">int32 highp_int32</div><div class="ttdoc">High qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00066">fwd.hpp:66</a></div></div>
<div class="ttc" id="a00304_html_ga7a6f1929464dcc680b16381a4ee5f2cf"><div class="ttname"><a href="a00304.html#ga7a6f1929464dcc680b16381a4ee5f2cf">glm::highp_u32</a></div><div class="ttdeci">uint32 highp_u32</div><div class="ttdoc">High qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00119">fwd.hpp:119</a></div></div>
<div class="ttc" id="a00304_html_ga727675ac6b5d2fc699520e0059735e25"><div class="ttname"><a href="a00304.html#ga727675ac6b5d2fc699520e0059735e25">glm::highp_i32</a></div><div class="ttdeci">int32 highp_i32</div><div class="ttdoc">High qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00061">fwd.hpp:61</a></div></div>
<div class="ttc" id="a00281_html_ga5abb4603dae0ce58c595e66d9123d812"><div class="ttname"><a href="a00281.html#ga5abb4603dae0ce58c595e66d9123d812">glm::ivec4</a></div><div class="ttdeci">vec&lt; 4, int, defaultp &gt; ivec4</div><div class="ttdoc">4 components vector of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00220_source.html#l00015">vector_int4.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_ga3945dd6515d4498cb603e65ff867ab03"><div class="ttname"><a href="a00304.html#ga3945dd6515d4498cb603e65ff867ab03">glm::u64vec4</a></div><div class="ttdeci">vec&lt; 4, u64, defaultp &gt; u64vec4</div><div class="ttdoc">Default qualifier 64 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00402">fwd.hpp:402</a></div></div>
<div class="ttc" id="a00304_html_ga31c6ca0e074a44007f49a9a3720b18c8"><div class="ttname"><a href="a00304.html#ga31c6ca0e074a44007f49a9a3720b18c8">glm::f32vec4</a></div><div class="ttdeci">vec&lt; 4, f32, defaultp &gt; f32vec4</div><div class="ttdoc">Single-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00464">fwd.hpp:464</a></div></div>
<div class="ttc" id="a00304_html_gae3ab5719fc4c1e966631dbbcba8d412a"><div class="ttname"><a href="a00304.html#gae3ab5719fc4c1e966631dbbcba8d412a">glm::f64mat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f64, defaultp &gt; f64mat2x3</div><div class="ttdoc">Double-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00783">fwd.hpp:783</a></div></div>
<div class="ttc" id="a00304_html_ga256b12b650c3f2fb86878fd1c5db8bc3"><div class="ttname"><a href="a00304.html#ga256b12b650c3f2fb86878fd1c5db8bc3">glm::highp_uint32</a></div><div class="ttdeci">uint32 highp_uint32</div><div class="ttdoc">High qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00124">fwd.hpp:124</a></div></div>
<div class="ttc" id="a00304_html_ga2473d8bf3f4abf967c4d0e18175be6f7"><div class="ttname"><a href="a00304.html#ga2473d8bf3f4abf967c4d0e18175be6f7">glm::f64mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f64, defaultp &gt; f64mat3x2</div><div class="ttdoc">Double-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00781">fwd.hpp:781</a></div></div>
<div class="ttc" id="a00304_html_gae627372cfd5f20dd87db490387b71195"><div class="ttname"><a href="a00304.html#gae627372cfd5f20dd87db490387b71195">glm::u32vec1</a></div><div class="ttdeci">vec&lt; 1, u32, defaultp &gt; u32vec1</div><div class="ttdoc">Default qualifier 32 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00379">fwd.hpp:379</a></div></div>
<div class="ttc" id="a00304_html_ga916c1aed91cf91f7b41399ebe7c6e185"><div class="ttname"><a href="a00304.html#ga916c1aed91cf91f7b41399ebe7c6e185">glm::f64mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f64, defaultp &gt; f64mat3x3</div><div class="ttdoc">Double-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00784">fwd.hpp:784</a></div></div>
<div class="ttc" id="a00304_html_ga97432f9979e73e66567361fd01e4cffb"><div class="ttname"><a href="a00304.html#ga97432f9979e73e66567361fd01e4cffb">glm::highp_uint8</a></div><div class="ttdeci">uint8 highp_uint8</div><div class="ttdoc">High qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00096">fwd.hpp:96</a></div></div>
<div class="ttc" id="a00304_html_gacb88796f2d08ef253d0345aff20c3aee"><div class="ttname"><a href="a00304.html#gacb88796f2d08ef253d0345aff20c3aee">glm::highp_i8</a></div><div class="ttdeci">int8 highp_i8</div><div class="ttdoc">High qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00033">fwd.hpp:33</a></div></div>
<div class="ttc" id="a00304_html_gacf1ded173e1e2d049c511d095b259e21"><div class="ttname"><a href="a00304.html#gacf1ded173e1e2d049c511d095b259e21">glm::mediump_i8</a></div><div class="ttdeci">int8 mediump_i8</div><div class="ttdoc">Medium qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00032">fwd.hpp:32</a></div></div>
<div class="ttc" id="a00304_html_ga73c6abb280a45feeff60f9accaee91f3"><div class="ttname"><a href="a00304.html#ga73c6abb280a45feeff60f9accaee91f3">glm::highp_int64_t</a></div><div class="ttdeci">int64 highp_int64_t</div><div class="ttdoc">High qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00084">fwd.hpp:84</a></div></div>
<div class="ttc" id="a00304_html_gaee4da0e9fbd8cfa2f89cb80889719dc3"><div class="ttname"><a href="a00304.html#gaee4da0e9fbd8cfa2f89cb80889719dc3">glm::f32mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, defaultp &gt; f32mat4x4</div><div class="ttdoc">Single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00708">fwd.hpp:708</a></div></div>
<div class="ttc" id="a00304_html_gaa4947bc8b47c72fceea9bda730ecf603"><div class="ttname"><a href="a00304.html#gaa4947bc8b47c72fceea9bda730ecf603">glm::float32_t</a></div><div class="ttdeci">float float32_t</div><div class="ttdoc">Default 32 bit single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00160">fwd.hpp:160</a></div></div>
<div class="ttc" id="a00304_html_ga04100c76f7d55a0dd0983ccf05142bff"><div class="ttname"><a href="a00304.html#ga04100c76f7d55a0dd0983ccf05142bff">glm::f32mat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, defaultp &gt; f32mat2x2</div><div class="ttdoc">Single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00700">fwd.hpp:700</a></div></div>
<div class="ttc" id="a00304_html_ga2171d9dc1fefb1c82e2817f45b622eac"><div class="ttname"><a href="a00304.html#ga2171d9dc1fefb1c82e2817f45b622eac">glm::uint32_t</a></div><div class="ttdeci">uint32 uint32_t</div><div class="ttdoc">Default qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00129">fwd.hpp:129</a></div></div>
<div class="ttc" id="a00303_html_ga76298aed82a439063c3d55980c84aa0b"><div class="ttname"><a href="a00303.html#ga76298aed82a439063c3d55980c84aa0b">glm::aligned_ivec1</a></div><div class="ttdeci">aligned_highp_ivec1 aligned_ivec1</div><div class="ttdoc">1 component vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01216">gtc/type_aligned.hpp:1216</a></div></div>
<div class="ttc" id="a00281_html_gac215a35481a6597d1bf622a382e9d6e2"><div class="ttname"><a href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">glm::vec4</a></div><div class="ttdeci">vec&lt; 4, float, defaultp &gt; vec4</div><div class="ttdoc">4 components vector of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00212_source.html#l00015">vector_float4.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_gaecc7082561fc9028b844b6cf3d305d36"><div class="ttname"><a href="a00304.html#gaecc7082561fc9028b844b6cf3d305d36">glm::u8</a></div><div class="ttdeci">uint8 u8</div><div class="ttdoc">Default qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00092">fwd.hpp:92</a></div></div>
<div class="ttc" id="a00304_html_gaacdc525d6f7bddb3ae95d5c311bd06a1"><div class="ttname"><a href="a00304.html#gaacdc525d6f7bddb3ae95d5c311bd06a1">glm::float32</a></div><div class="ttdeci">float float32</div><div class="ttdoc">Single-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00155">fwd.hpp:155</a></div></div>
<div class="ttc" id="a00304_html_ga713c796c54875cf4092d42ff9d9096b0"><div class="ttname"><a href="a00304.html#ga713c796c54875cf4092d42ff9d9096b0">glm::fvec4</a></div><div class="ttdeci">vec&lt; 4, f32, defaultp &gt; fvec4</div><div class="ttdoc">Single-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00444">fwd.hpp:444</a></div></div>
<div class="ttc" id="a00304_html_ga08c05ba8ffb19f5d14ab584e1e9e9ee5"><div class="ttname"><a href="a00304.html#ga08c05ba8ffb19f5d14ab584e1e9e9ee5">glm::u16vec1</a></div><div class="ttdeci">vec&lt; 1, u16, defaultp &gt; u16vec1</div><div class="ttdoc">Default qualifier 16 bit unsigned integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00359">fwd.hpp:359</a></div></div>
<div class="ttc" id="a00268_html_ga6221af17edc2d4477a4583d2cd53e569"><div class="ttname"><a href="a00268.html#ga6221af17edc2d4477a4583d2cd53e569">glm::dvec1</a></div><div class="ttdeci">vec&lt; 1, double, defaultp &gt; dvec1</div><div class="ttdoc">1 components vector of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00198_source.html#l00028">vector_double1.hpp:28</a></div></div>
<div class="ttc" id="a00304_html_ga7e80d927ff0a3861ced68dfff8a4020b"><div class="ttname"><a href="a00304.html#ga7e80d927ff0a3861ced68dfff8a4020b">glm::i8vec1</a></div><div class="ttdeci">vec&lt; 1, i8, defaultp &gt; i8vec1</div><div class="ttdoc">8 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00237">fwd.hpp:237</a></div></div>
<div class="ttc" id="a00304_html_ga8b44026374982dcd1e52d22bac99247e"><div class="ttname"><a href="a00304.html#ga8b44026374982dcd1e52d22bac99247e">glm::i32vec2</a></div><div class="ttdeci">vec&lt; 2, i32, defaultp &gt; i32vec2</div><div class="ttdoc">32 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00278">fwd.hpp:278</a></div></div>
<div class="ttc" id="a00304_html_gac4e00a26a2adb5f2c0a7096810df29e5"><div class="ttname"><a href="a00304.html#gac4e00a26a2adb5f2c0a7096810df29e5">glm::highp_uint8_t</a></div><div class="ttdeci">uint8 highp_uint8_t</div><div class="ttdoc">High qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00100">fwd.hpp:100</a></div></div>
<div class="ttc" id="a00304_html_ga30652709815ad9404272a31957daa59e"><div class="ttname"><a href="a00304.html#ga30652709815ad9404272a31957daa59e">glm::mediump_uint64</a></div><div class="ttdeci">uint64 mediump_uint64</div><div class="ttdoc">Medium qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00137">fwd.hpp:137</a></div></div>
<div class="ttc" id="a00304_html_ga2c71c8bd9e2fe7d2e93ca250d8b6157f"><div class="ttname"><a href="a00304.html#ga2c71c8bd9e2fe7d2e93ca250d8b6157f">glm::highp_int32_t</a></div><div class="ttdeci">int32 highp_int32_t</div><div class="ttdoc">32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00070">fwd.hpp:70</a></div></div>
<div class="ttc" id="a00304_html_gaa7a1ddca75c5f629173bf4772db7a635"><div class="ttname"><a href="a00304.html#gaa7a1ddca75c5f629173bf4772db7a635">glm::f64vec3</a></div><div class="ttdeci">vec&lt; 3, f64, defaultp &gt; f64vec3</div><div class="ttdoc">Double-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00503">fwd.hpp:503</a></div></div>
<div class="ttc" id="a00304_html_gac87278e0c702ba8afff76316d4eeb769"><div class="ttname"><a href="a00304.html#gac87278e0c702ba8afff76316d4eeb769">glm::f64mat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f64, defaultp &gt; f64mat2x4</div><div class="ttdoc">Double-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00786">fwd.hpp:786</a></div></div>
<div class="ttc" id="a00283_html_gab91887d7565059dac640e3a1921c914a"><div class="ttname"><a href="a00283.html#gab91887d7565059dac640e3a1921c914a">glm::mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, float, defaultp &gt; mat3x3</div><div class="ttdoc">3 columns of 3 components matrix of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00090_source.html#l00015">matrix_float3x3.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_ga2af9490085ae3bdf36a544e9dd073610"><div class="ttname"><a href="a00304.html#ga2af9490085ae3bdf36a544e9dd073610">glm::mediump_u64</a></div><div class="ttdeci">uint64 mediump_u64</div><div class="ttdoc">Medium qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00132">fwd.hpp:132</a></div></div>
<div class="ttc" id="a00281_html_ga2f6d9ec3ae14813ade37d6aee3715fdb"><div class="ttname"><a href="a00281.html#ga2f6d9ec3ae14813ade37d6aee3715fdb">glm::uvec2</a></div><div class="ttdeci">vec&lt; 2, unsigned int, defaultp &gt; uvec2</div><div class="ttdoc">2 components vector of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00228_source.html#l00015">vector_uint2.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_ga504ce1631cb2ac02fcf1d44d8c2aa126"><div class="ttname"><a href="a00304.html#ga504ce1631cb2ac02fcf1d44d8c2aa126">glm::lowp_u16</a></div><div class="ttdeci">uint16 lowp_u16</div><div class="ttdoc">Low qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00103">fwd.hpp:103</a></div></div>
<div class="ttc" id="a00276_html_gac3bdd96183d23876c58a1424585fefe7"><div class="ttname"><a href="a00276.html#gac3bdd96183d23876c58a1424585fefe7">glm::uvec1</a></div><div class="ttdeci">vec&lt; 1, unsigned int, defaultp &gt; uvec1</div><div class="ttdoc">1 component vector of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00226_source.html#l00028">vector_uint1.hpp:28</a></div></div>
<div class="ttc" id="a00304_html_ga0336abc2604dd2c20c30e036454b64f8"><div class="ttname"><a href="a00304.html#ga0336abc2604dd2c20c30e036454b64f8">glm::highp_i16</a></div><div class="ttdeci">int16 highp_i16</div><div class="ttdoc">High qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00047">fwd.hpp:47</a></div></div>
<div class="ttc" id="a00304_html_gad0549c902a96a7164e4ac858d5f39dbf"><div class="ttname"><a href="a00304.html#gad0549c902a96a7164e4ac858d5f39dbf">glm::highp_int8</a></div><div class="ttdeci">int8 highp_int8</div><div class="ttdoc">High qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00038">fwd.hpp:38</a></div></div>
<div class="ttc" id="a00304_html_gae52e2b7077a9ff928a06ab5ce600b81e"><div class="ttname"><a href="a00304.html#gae52e2b7077a9ff928a06ab5ce600b81e">glm::f64mat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f64, defaultp &gt; f64mat4x4</div><div class="ttdoc">Double-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00788">fwd.hpp:788</a></div></div>
<div class="ttc" id="a00304_html_ga4295048a78bdf46b8a7de77ec665b497"><div class="ttname"><a href="a00304.html#ga4295048a78bdf46b8a7de77ec665b497">glm::fmat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f32, defaultp &gt; fmat4x3</div><div class="ttdoc">Single-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00665">fwd.hpp:665</a></div></div>
<div class="ttc" id="a00304_html_ga89930533646b30d021759298aa6bf04a"><div class="ttname"><a href="a00304.html#ga89930533646b30d021759298aa6bf04a">glm::fvec3</a></div><div class="ttdeci">vec&lt; 3, f32, defaultp &gt; fvec3</div><div class="ttdoc">Single-qualifier floating-point vector of 3 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00443">fwd.hpp:443</a></div></div>
<div class="ttc" id="a00304_html_ga2996630ba7b10535af8e065cf326f761"><div class="ttname"><a href="a00304.html#ga2996630ba7b10535af8e065cf326f761">glm::i16vec2</a></div><div class="ttdeci">vec&lt; 2, i16, defaultp &gt; i16vec2</div><div class="ttdoc">16 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00258">fwd.hpp:258</a></div></div>
<div class="ttc" id="a00304_html_ga239b96198771b7add8eea7e6b59840c0"><div class="ttname"><a href="a00304.html#ga239b96198771b7add8eea7e6b59840c0">glm::f32mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f32, defaultp &gt; f32mat4x3</div><div class="ttdoc">Single-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00705">fwd.hpp:705</a></div></div>
<div class="ttc" id="a00304_html_ga392b673fd10847bfb78fb808c6cf8ff7"><div class="ttname"><a href="a00304.html#ga392b673fd10847bfb78fb808c6cf8ff7">glm::lowp_i16</a></div><div class="ttdeci">int16 lowp_i16</div><div class="ttdoc">Low qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00045">fwd.hpp:45</a></div></div>
<div class="ttc" id="a00270_html_gadfc071d934d8dae7955a1d530a3cf656"><div class="ttname"><a href="a00270.html#gadfc071d934d8dae7955a1d530a3cf656">glm::vec1</a></div><div class="ttdeci">vec&lt; 1, float, defaultp &gt; vec1</div><div class="ttdoc">1 components vector of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00206_source.html#l00028">vector_float1.hpp:28</a></div></div>
<div class="ttc" id="a00303_html_gab0fddcf95dd51cbcbf624ea7c40dfeb8"><div class="ttname"><a href="a00303.html#gab0fddcf95dd51cbcbf624ea7c40dfeb8">glm::aligned_mat4</a></div><div class="ttdeci">aligned_highp_mat4 aligned_mat4</div><div class="ttdoc">4 by 4 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00970">gtc/type_aligned.hpp:970</a></div></div>
<div class="ttc" id="a00304_html_ga728366fef72cd96f0a5fa6429f05469e"><div class="ttname"><a href="a00304.html#ga728366fef72cd96f0a5fa6429f05469e">glm::float64_t</a></div><div class="ttdeci">double float64_t</div><div class="ttdoc">Default 64 bit double-qualifier floating-point scalar. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00176">fwd.hpp:176</a></div></div>
<div class="ttc" id="a00304_html_ga698e36b01167fc0f037889334dce8def"><div class="ttname"><a href="a00304.html#ga698e36b01167fc0f037889334dce8def">glm::lowp_int16</a></div><div class="ttdeci">int16 lowp_int16</div><div class="ttdoc">Low qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00050">fwd.hpp:50</a></div></div>
<div class="ttc" id="a00303_html_ga1ff8ed402c93d280ff0597c1c5e7c548"><div class="ttname"><a href="a00303.html#ga1ff8ed402c93d280ff0597c1c5e7c548">glm::aligned_uvec1</a></div><div class="ttdeci">aligned_highp_uvec1 aligned_uvec1</div><div class="ttdoc">1 component vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01254">gtc/type_aligned.hpp:1254</a></div></div>
<div class="ttc" id="a00304_html_gaebf341fc4a5be233f7dde962c2e33847"><div class="ttname"><a href="a00304.html#gaebf341fc4a5be233f7dde962c2e33847">glm::lowp_int64_t</a></div><div class="ttdeci">int64 lowp_int64_t</div><div class="ttdoc">Low qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00082">fwd.hpp:82</a></div></div>
<div class="ttc" id="a00304_html_ga91f91f411080c37730856ff5887f5bcf"><div class="ttname"><a href="a00304.html#ga91f91f411080c37730856ff5887f5bcf">glm::uint16_t</a></div><div class="ttdeci">uint16 uint16_t</div><div class="ttdoc">Default qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00115">fwd.hpp:115</a></div></div>
<div class="ttc" id="a00303_html_ga05e6d4c908965d04191c2070a8d0a65e"><div class="ttname"><a href="a00303.html#ga05e6d4c908965d04191c2070a8d0a65e">glm::aligned_vec1</a></div><div class="ttdeci">aligned_highp_vec1 aligned_vec1</div><div class="ttdoc">1 component vector aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00940">gtc/type_aligned.hpp:940</a></div></div>
<div class="ttc" id="a00304_html_ga864aabca5f3296e176e0c3ed9cc16b02"><div class="ttname"><a href="a00304.html#ga864aabca5f3296e176e0c3ed9cc16b02">glm::lowp_int32</a></div><div class="ttdeci">int32 lowp_int32</div><div class="ttdoc">Low qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00064">fwd.hpp:64</a></div></div>
<div class="ttc" id="a00304_html_ga28d97808322d3c92186e4a0c067d7e8e"><div class="ttname"><a href="a00304.html#ga28d97808322d3c92186e4a0c067d7e8e">glm::uint8_t</a></div><div class="ttdeci">uint8 uint8_t</div><div class="ttdoc">Default qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00101">fwd.hpp:101</a></div></div>
<div class="ttc" id="a00304_html_ga26fc7ced1ad7ca5024f1c973c8dc9180"><div class="ttname"><a href="a00304.html#ga26fc7ced1ad7ca5024f1c973c8dc9180">glm::mediump_int32_t</a></div><div class="ttdeci">int32 mediump_int32_t</div><div class="ttdoc">Medium qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00069">fwd.hpp:69</a></div></div>
<div class="ttc" id="a00304_html_ga65261fa8a21045c8646ddff114a56174"><div class="ttname"><a href="a00304.html#ga65261fa8a21045c8646ddff114a56174">glm::f32mat3x3</a></div><div class="ttdeci">mat&lt; 3, 3, f32, defaultp &gt; f32mat3x3</div><div class="ttdoc">Single-qualifier floating-point 3x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00704">fwd.hpp:704</a></div></div>
<div class="ttc" id="a00304_html_gacd1259f3a9e8d2a9df5be2d74322ef9c"><div class="ttname"><a href="a00304.html#gacd1259f3a9e8d2a9df5be2d74322ef9c">glm::highp_u8</a></div><div class="ttdeci">uint8 highp_u8</div><div class="ttdoc">High qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00091">fwd.hpp:91</a></div></div>
<div class="ttc" id="a00304_html_ga1fa92a233b9110861cdbc8c2ccf0b5a3"><div class="ttname"><a href="a00304.html#ga1fa92a233b9110861cdbc8c2ccf0b5a3">glm::mediump_uint8</a></div><div class="ttdeci">uint8 mediump_uint8</div><div class="ttdoc">Medium qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00095">fwd.hpp:95</a></div></div>
<div class="ttc" id="a00303_html_gabf842c45eea186170c267a328e3f3b7d"><div class="ttname"><a href="a00303.html#gabf842c45eea186170c267a328e3f3b7d">glm::aligned_uvec4</a></div><div class="ttdeci">aligned_highp_uvec4 aligned_uvec4</div><div class="ttdoc">4 components vector aligned in memory of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01263">gtc/type_aligned.hpp:1263</a></div></div>
<div class="ttc" id="a00304_html_gac3bc41bcac61d1ba8f02a6f68ce23f64"><div class="ttname"><a href="a00304.html#gac3bc41bcac61d1ba8f02a6f68ce23f64">glm::mediump_int64_t</a></div><div class="ttdeci">int64 mediump_int64_t</div><div class="ttdoc">Medium qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00083">fwd.hpp:83</a></div></div>
<div class="ttc" id="a00303_html_ga7f79eae5927c9033d84617e49f6f34e4"><div class="ttname"><a href="a00303.html#ga7f79eae5927c9033d84617e49f6f34e4">glm::aligned_ivec4</a></div><div class="ttdeci">aligned_highp_ivec4 aligned_ivec4</div><div class="ttdoc">4 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01225">gtc/type_aligned.hpp:1225</a></div></div>
<div class="ttc" id="a00304_html_ga1085c50dd8fbeb5e7e609b1c127492a5"><div class="ttname"><a href="a00304.html#ga1085c50dd8fbeb5e7e609b1c127492a5">glm::highp_int8_t</a></div><div class="ttdeci">int8 highp_int8_t</div><div class="ttdoc">High qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00042">fwd.hpp:42</a></div></div>
<div class="ttc" id="a00304_html_ga1320a08e14fdff3821241eefab6947e9"><div class="ttname"><a href="a00304.html#ga1320a08e14fdff3821241eefab6947e9">glm::f32mat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f32, defaultp &gt; f32mat3x2</div><div class="ttdoc">Single-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00701">fwd.hpp:701</a></div></div>
<div class="ttc" id="a00304_html_ga866a05905c49912309ed1fa5f5980e61"><div class="ttname"><a href="a00304.html#ga866a05905c49912309ed1fa5f5980e61">glm::i32vec4</a></div><div class="ttdeci">vec&lt; 4, i32, defaultp &gt; i32vec4</div><div class="ttdoc">32 bit signed integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00280">fwd.hpp:280</a></div></div>
<div class="ttc" id="a00304_html_gae267358ff2a41d156d97f5762630235a"><div class="ttname"><a href="a00304.html#gae267358ff2a41d156d97f5762630235a">glm::u32vec3</a></div><div class="ttdeci">vec&lt; 3, u32, defaultp &gt; u32vec3</div><div class="ttdoc">Default qualifier 32 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00381">fwd.hpp:381</a></div></div>
<div class="ttc" id="a00304_html_ga518b8d948a6b4ddb72f84d5c3b7b6611"><div class="ttname"><a href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">glm::u8vec2</a></div><div class="ttdeci">vec&lt; 2, u8, defaultp &gt; u8vec2</div><div class="ttdoc">Default qualifier 8 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00340">fwd.hpp:340</a></div></div>
<div class="ttc" id="a00304_html_ga62a17cddeb4dffb4e18fe3aea23f051a"><div class="ttname"><a href="a00304.html#ga62a17cddeb4dffb4e18fe3aea23f051a">glm::mediump_i16</a></div><div class="ttdeci">int16 mediump_i16</div><div class="ttdoc">Medium qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00046">fwd.hpp:46</a></div></div>
<div class="ttc" id="a00304_html_ga5a08d36cf7917cd19d081a603d0eae3e"><div class="ttname"><a href="a00304.html#ga5a08d36cf7917cd19d081a603d0eae3e">glm::i8vec3</a></div><div class="ttdeci">vec&lt; 3, i8, defaultp &gt; i8vec3</div><div class="ttdoc">8 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00239">fwd.hpp:239</a></div></div>
<div class="ttc" id="a00303_html_ga0682462f8096a226773e20fac993cde5"><div class="ttname"><a href="a00303.html#ga0682462f8096a226773e20fac993cde5">glm::aligned_vec2</a></div><div class="ttdeci">aligned_highp_vec2 aligned_vec2</div><div class="ttdoc">2 components vector aligned in memory of single-precision floating-point numbers. ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00943">gtc/type_aligned.hpp:943</a></div></div>
<div class="ttc" id="a00283_html_ga0db98d836c5549d31cf64ecd043b7af7"><div class="ttname"><a href="a00283.html#ga0db98d836c5549d31cf64ecd043b7af7">glm::mat4</a></div><div class="ttdeci">mat&lt; 4, 4, float, defaultp &gt; mat4</div><div class="ttdoc">4 columns of 4 components matrix of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00098_source.html#l00020">matrix_float4x4.hpp:20</a></div></div>
<div class="ttc" id="a00303_html_gabb04f459d81d753d278b2072e2375e8e"><div class="ttname"><a href="a00303.html#gabb04f459d81d753d278b2072e2375e8e">glm::aligned_mat2x2</a></div><div class="ttdeci">aligned_highp_mat2x2 aligned_mat2x2</div><div class="ttdoc">2 by 2 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00982">gtc/type_aligned.hpp:982</a></div></div>
<div class="ttc" id="a00304_html_ga3963b1050fc65a383ee28e3f827b6e3e"><div class="ttname"><a href="a00304.html#ga3963b1050fc65a383ee28e3f827b6e3e">glm::mediump_uint16_t</a></div><div class="ttdeci">uint16 mediump_uint16_t</div><div class="ttdoc">Medium qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00113">fwd.hpp:113</a></div></div>
<div class="ttc" id="a00304_html_ga7c5706f6bbe5282e5598acf7e7b377e2"><div class="ttname"><a href="a00304.html#ga7c5706f6bbe5282e5598acf7e7b377e2">glm::u8vec3</a></div><div class="ttdeci">vec&lt; 3, u8, defaultp &gt; u8vec3</div><div class="ttdoc">Default qualifier 8 bit unsigned integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00341">fwd.hpp:341</a></div></div>
<div class="ttc" id="a00304_html_ga7b968f2b86a0442a89c7359171e1d866"><div class="ttname"><a href="a00304.html#ga7b968f2b86a0442a89c7359171e1d866">glm::mediump_int64</a></div><div class="ttdeci">int64 mediump_int64</div><div class="ttdoc">Medium qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00079">fwd.hpp:79</a></div></div>
<div class="ttc" id="a00304_html_ga3999d3e7ff22025c16ddb601e14dfdee"><div class="ttname"><a href="a00304.html#ga3999d3e7ff22025c16ddb601e14dfdee">glm::uint64_t</a></div><div class="ttdeci">uint64 uint64_t</div><div class="ttdoc">Default qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00143">fwd.hpp:143</a></div></div>
<div class="ttc" id="a00304_html_ga7f526b5cccef126a2ebcf9bdd890394e"><div class="ttname"><a href="a00304.html#ga7f526b5cccef126a2ebcf9bdd890394e">glm::i32vec3</a></div><div class="ttdeci">vec&lt; 3, i32, defaultp &gt; i32vec3</div><div class="ttdoc">32 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00279">fwd.hpp:279</a></div></div>
<div class="ttc" id="a00304_html_ga91c4815f93177eb423362fd296a87e9f"><div class="ttname"><a href="a00304.html#ga91c4815f93177eb423362fd296a87e9f">glm::lowp_uint16_t</a></div><div class="ttdeci">uint16 lowp_uint16_t</div><div class="ttdoc">Low qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00112">fwd.hpp:112</a></div></div>
<div class="ttc" id="a00281_html_ga8b09c71aaac7da7867ae58377fe219a8"><div class="ttname"><a href="a00281.html#ga8b09c71aaac7da7867ae58377fe219a8">glm::dvec2</a></div><div class="ttdeci">vec&lt; 2, double, defaultp &gt; dvec2</div><div class="ttdoc">2 components vector of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00200_source.html#l00015">vector_double2.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_gad68bfd9f881856fc863a6ebca0b67f78"><div class="ttname"><a href="a00304.html#gad68bfd9f881856fc863a6ebca0b67f78">glm::lowp_uint16</a></div><div class="ttdeci">uint16 lowp_uint16</div><div class="ttdoc">Low qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00108">fwd.hpp:108</a></div></div>
<div class="ttc" id="a00304_html_ga66e92e57260bdb910609b9a56bf83e97"><div class="ttname"><a href="a00304.html#ga66e92e57260bdb910609b9a56bf83e97">glm::f64vec4</a></div><div class="ttdeci">vec&lt; 4, f64, defaultp &gt; f64vec4</div><div class="ttdoc">Double-qualifier floating-point vector of 4 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00504">fwd.hpp:504</a></div></div>
<div class="ttc" id="a00304_html_ga7ff73a45cea9613ebf1a9fad0b9f82ac"><div class="ttname"><a href="a00304.html#ga7ff73a45cea9613ebf1a9fad0b9f82ac">glm::lowp_i32</a></div><div class="ttdeci">int32 lowp_i32</div><div class="ttdoc">Low qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00059">fwd.hpp:59</a></div></div>
<div class="ttc" id="a00281_html_ga9c3019b13faf179e4ad3626ea66df334"><div class="ttname"><a href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">glm::vec3</a></div><div class="ttdeci">vec&lt; 3, float, defaultp &gt; vec3</div><div class="ttdoc">3 components vector of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00210_source.html#l00015">vector_float3.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_ga3ebcb1f6d8d8387253de8bccb058d77f"><div class="ttname"><a href="a00304.html#ga3ebcb1f6d8d8387253de8bccb058d77f">glm::mediump_i64</a></div><div class="ttdeci">int64 mediump_i64</div><div class="ttdoc">Medium qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00074">fwd.hpp:74</a></div></div>
<div class="ttc" id="a00304_html_ga24273aa02abaecaab7f160bac437a339"><div class="ttname"><a href="a00304.html#ga24273aa02abaecaab7f160bac437a339">glm::fvec2</a></div><div class="ttdeci">vec&lt; 2, f32, defaultp &gt; fvec2</div><div class="ttdoc">Single-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00442">fwd.hpp:442</a></div></div>
<div class="ttc" id="a00303_html_gae4f38fd2c86cee6940986197777b3ca4"><div class="ttname"><a href="a00303.html#gae4f38fd2c86cee6940986197777b3ca4">glm::aligned_ivec2</a></div><div class="ttdeci">aligned_highp_ivec2 aligned_ivec2</div><div class="ttdoc">2 components vector aligned in memory of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01219">gtc/type_aligned.hpp:1219</a></div></div>
<div class="ttc" id="a00304_html_gae8f5e3e964ca2ae240adc2c0d74adede"><div class="ttname"><a href="a00304.html#gae8f5e3e964ca2ae240adc2c0d74adede">glm::int16_t</a></div><div class="ttdeci">int16 int16_t</div><div class="ttdoc">16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00057">fwd.hpp:57</a></div></div>
<div class="ttc" id="a00304_html_gac25db6d2b1e2a0f351b77ba3409ac4cd"><div class="ttname"><a href="a00304.html#gac25db6d2b1e2a0f351b77ba3409ac4cd">glm::highp_i64</a></div><div class="ttdeci">int64 highp_i64</div><div class="ttdoc">High qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00075">fwd.hpp:75</a></div></div>
<div class="ttc" id="a00304_html_ga042ef09ff2f0cb24a36f541bcb3a3710"><div class="ttname"><a href="a00304.html#ga042ef09ff2f0cb24a36f541bcb3a3710">glm::int32_t</a></div><div class="ttdeci">int32 int32_t</div><div class="ttdoc">32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00071">fwd.hpp:71</a></div></div>
<div class="ttc" id="a00304_html_gadc4e1594f9555d919131ee02b17822a2"><div class="ttname"><a href="a00304.html#gadc4e1594f9555d919131ee02b17822a2">glm::f64vec2</a></div><div class="ttdeci">vec&lt; 2, f64, defaultp &gt; f64vec2</div><div class="ttdoc">Double-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00502">fwd.hpp:502</a></div></div>
<div class="ttc" id="a00281_html_gaa57e96bb337867329d5f43bcc27c1095"><div class="ttname"><a href="a00281.html#gaa57e96bb337867329d5f43bcc27c1095">glm::uvec4</a></div><div class="ttdeci">vec&lt; 4, unsigned int, defaultp &gt; uvec4</div><div class="ttdoc">4 components vector of unsigned integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00232_source.html#l00015">vector_uint4.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_ga8dd3a3281ae5c970ffe0c41d538aa153"><div class="ttname"><a href="a00304.html#ga8dd3a3281ae5c970ffe0c41d538aa153">glm::lowp_uint64_t</a></div><div class="ttdeci">uint64 lowp_uint64_t</div><div class="ttdoc">Low qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00140">fwd.hpp:140</a></div></div>
<div class="ttc" id="a00263_html_gab630f76c26b50298187f7889104d4b9c"><div class="ttname"><a href="a00263.html#gab630f76c26b50298187f7889104d4b9c">glm::uint64</a></div><div class="ttdeci">detail::uint64 uint64</div><div class="ttdoc">64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00151_source.html#l00067">scalar_uint_sized.hpp:67</a></div></div>
<div class="ttc" id="a00304_html_ga5fde0fa4a3852a9dd5d637a92ee74718"><div class="ttname"><a href="a00304.html#ga5fde0fa4a3852a9dd5d637a92ee74718">glm::highp_int16</a></div><div class="ttdeci">int16 highp_int16</div><div class="ttdoc">High qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00052">fwd.hpp:52</a></div></div>
<div class="ttc" id="a00303_html_ga8b8fb86973a0b768c5bd802c92fac1a1"><div class="ttname"><a href="a00303.html#ga8b8fb86973a0b768c5bd802c92fac1a1">glm::aligned_mat4x4</a></div><div class="ttdeci">aligned_highp_mat4x4 aligned_mat4x4</div><div class="ttdoc">4 by 4 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01006">gtc/type_aligned.hpp:1006</a></div></div>
<div class="ttc" id="a00304_html_ga681381f19f11c9e5ee45cda2c56937ff"><div class="ttname"><a href="a00304.html#ga681381f19f11c9e5ee45cda2c56937ff">glm::fmat2x4</a></div><div class="ttdeci">mat&lt; 2, 4, f32, defaultp &gt; fmat2x4</div><div class="ttdoc">Single-qualifier floating-point 2x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00666">fwd.hpp:666</a></div></div>
<div class="ttc" id="a00304_html_gaf5e94bf2a20af7601787c154751dc2e1"><div class="ttname"><a href="a00304.html#gaf5e94bf2a20af7601787c154751dc2e1">glm::mediump_i32</a></div><div class="ttdeci">int32 mediump_i32</div><div class="ttdoc">Medium qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00060">fwd.hpp:60</a></div></div>
<div class="ttc" id="a00303_html_ga8a9f0a4795ccc442fa9901845026f9f5"><div class="ttname"><a href="a00303.html#ga8a9f0a4795ccc442fa9901845026f9f5">glm::aligned_dvec4</a></div><div class="ttdeci">aligned_highp_dvec4 aligned_dvec4</div><div class="ttdoc">4 components vector aligned in memory of double-precision floating-point numbers. ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01117">gtc/type_aligned.hpp:1117</a></div></div>
<div class="ttc" id="a00304_html_gaa46172d7dc1c7ffe3e78107ff88adf08"><div class="ttname"><a href="a00304.html#gaa46172d7dc1c7ffe3e78107ff88adf08">glm::highp_uint64_t</a></div><div class="ttdeci">uint64 highp_uint64_t</div><div class="ttdoc">High qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00142">fwd.hpp:142</a></div></div>
<div class="ttc" id="a00304_html_ga31cef34e4cd04840c54741ff2f7005f0"><div class="ttname"><a href="a00304.html#ga31cef34e4cd04840c54741ff2f7005f0">glm::u32vec4</a></div><div class="ttdeci">vec&lt; 4, u32, defaultp &gt; u32vec4</div><div class="ttdoc">Default qualifier 32 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00382">fwd.hpp:382</a></div></div>
<div class="ttc" id="a00304_html_ga38e674196ba411d642be40c47bf33939"><div class="ttname"><a href="a00304.html#ga38e674196ba411d642be40c47bf33939">glm::f32quat</a></div><div class="ttdeci">qua&lt; f32, defaultp &gt; f32quat</div><div class="ttdoc">Single-qualifier floating-point quaternion. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00805">fwd.hpp:805</a></div></div>
<div class="ttc" id="a00260_html_gaff5189f97f9e842d9636a0f240001b2e"><div class="ttname"><a href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">glm::int64</a></div><div class="ttdeci">detail::int64 int64</div><div class="ttdoc">64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00146_source.html#l00067">scalar_int_sized.hpp:67</a></div></div>
<div class="ttc" id="a00304_html_gab7daf79d6bc06a68bea1c6f5e11b5512"><div class="ttname"><a href="a00304.html#gab7daf79d6bc06a68bea1c6f5e11b5512">glm::f64mat4x2</a></div><div class="ttdeci">mat&lt; 4, 2, f64, defaultp &gt; f64mat4x2</div><div class="ttdoc">Double-qualifier floating-point 4x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00782">fwd.hpp:782</a></div></div>
<div class="ttc" id="a00304_html_ga55a2d2a8eb09b5633668257eb3cad453"><div class="ttname"><a href="a00304.html#ga55a2d2a8eb09b5633668257eb3cad453">glm::fmat2x3</a></div><div class="ttdeci">mat&lt; 2, 3, f32, defaultp &gt; fmat2x3</div><div class="ttdoc">Single-qualifier floating-point 2x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00663">fwd.hpp:663</a></div></div>
<div class="ttc" id="a00304_html_gaa2d7acc0adb536fab71fe261232a40ff"><div class="ttname"><a href="a00304.html#gaa2d7acc0adb536fab71fe261232a40ff">glm::u16</a></div><div class="ttdeci">uint16 u16</div><div class="ttdoc">Default qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00106">fwd.hpp:106</a></div></div>
<div class="ttc" id="a00304_html_ga354736e0c645099cd44c42fb2f87c2b8"><div class="ttname"><a href="a00304.html#ga354736e0c645099cd44c42fb2f87c2b8">glm::lowp_i64</a></div><div class="ttdeci">int64 lowp_i64</div><div class="ttdoc">Low qualifier 64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00073">fwd.hpp:73</a></div></div>
<div class="ttc" id="a00281_html_ga6f9269106d91b2d2b91bcf27cd5f5560"><div class="ttname"><a href="a00281.html#ga6f9269106d91b2d2b91bcf27cd5f5560">glm::ivec2</a></div><div class="ttdeci">vec&lt; 2, int, defaultp &gt; ivec2</div><div class="ttdoc">2 components vector of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00216_source.html#l00015">vector_int2.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_ga6d7b3789ecb932c26430009478cac7ae"><div class="ttname"><a href="a00304.html#ga6d7b3789ecb932c26430009478cac7ae">glm::mediump_int8_t</a></div><div class="ttdeci">int8 mediump_int8_t</div><div class="ttdoc">Medium qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00041">fwd.hpp:41</a></div></div>
<div class="ttc" id="a00304_html_gacaea06d0a79ef3172e887a7a6ba434ff"><div class="ttname"><a href="a00304.html#gacaea06d0a79ef3172e887a7a6ba434ff">glm::highp_int16_t</a></div><div class="ttdeci">int16 highp_int16_t</div><div class="ttdoc">High qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00056">fwd.hpp:56</a></div></div>
<div class="ttc" id="a00304_html_ga2b65767f8b5aed1bd1cf86c541662b50"><div class="ttname"><a href="a00304.html#ga2b65767f8b5aed1bd1cf86c541662b50">glm::i64vec1</a></div><div class="ttdeci">vec&lt; 1, i64, defaultp &gt; i64vec1</div><div class="ttdoc">64 bit signed integer scalar type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00297">fwd.hpp:297</a></div></div>
<div class="ttc" id="a00303_html_ga85d89e83cb8137e1be1446de8c3b643a"><div class="ttname"><a href="a00303.html#ga85d89e83cb8137e1be1446de8c3b643a">glm::aligned_vec4</a></div><div class="ttdeci">aligned_highp_vec4 aligned_vec4</div><div class="ttdoc">4 components vector aligned in memory of single-precision floating-point numbers. ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00949">gtc/type_aligned.hpp:949</a></div></div>
<div class="ttc" id="a00304_html_ga4f072ada9552e1e480bbb3b1acde5250"><div class="ttname"><a href="a00304.html#ga4f072ada9552e1e480bbb3b1acde5250">glm::lowp_u32</a></div><div class="ttdeci">uint32 lowp_u32</div><div class="ttdoc">Low qualifier 32 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00117">fwd.hpp:117</a></div></div>
<div class="ttc" id="a00272_html_gaedd0562c2e77714929d7723a7e2e0dba"><div class="ttname"><a href="a00272.html#gaedd0562c2e77714929d7723a7e2e0dba">glm::ivec1</a></div><div class="ttdeci">vec&lt; 1, int, defaultp &gt; ivec1</div><div class="ttdoc">1 component vector of signed integer numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00214_source.html#l00028">vector_int1.hpp:28</a></div></div>
<div class="ttc" id="a00304_html_ga8e62c883d13f47015f3b70ed88751369"><div class="ttname"><a href="a00304.html#ga8e62c883d13f47015f3b70ed88751369">glm::highp_u16</a></div><div class="ttdeci">uint16 highp_u16</div><div class="ttdoc">High qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00105">fwd.hpp:105</a></div></div>
<div class="ttc" id="a00304_html_ga98b9ed43cf8c5cf1d354b23c7df9119f"><div class="ttname"><a href="a00304.html#ga98b9ed43cf8c5cf1d354b23c7df9119f">glm::fvec1</a></div><div class="ttdeci">vec&lt; 1, f32, defaultp &gt; fvec1</div><div class="ttdoc">Single-qualifier floating-point vector of 1 component. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00441">fwd.hpp:441</a></div></div>
<div class="ttc" id="a00304_html_ga0350631d35ff800e6133ac6243b13cbc"><div class="ttname"><a href="a00304.html#ga0350631d35ff800e6133ac6243b13cbc">glm::lowp_int32_t</a></div><div class="ttdeci">int32 lowp_int32_t</div><div class="ttdoc">Low qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00068">fwd.hpp:68</a></div></div>
<div class="ttc" id="a00304_html_ga5d6c70e080409a76a257dc55bd8ea2c8"><div class="ttname"><a href="a00304.html#ga5d6c70e080409a76a257dc55bd8ea2c8">glm::f32vec2</a></div><div class="ttdeci">vec&lt; 2, f32, defaultp &gt; f32vec2</div><div class="ttdoc">Single-qualifier floating-point vector of 2 components. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00462">fwd.hpp:462</a></div></div>
<div class="ttc" id="a00304_html_ga3350c93c3275298f940a42875388e4b4"><div class="ttname"><a href="a00304.html#ga3350c93c3275298f940a42875388e4b4">glm::fmat2x2</a></div><div class="ttdeci">mat&lt; 2, 2, f32, defaultp &gt; fmat2x2</div><div class="ttdoc">Single-qualifier floating-point 1x1 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00660">fwd.hpp:660</a></div></div>
<div class="ttc" id="a00304_html_ga760bcf26fdb23a2c3ecad3c928a19ae6"><div class="ttname"><a href="a00304.html#ga760bcf26fdb23a2c3ecad3c928a19ae6">glm::lowp_int8</a></div><div class="ttdeci">int8 lowp_int8</div><div class="ttdoc">Low qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00036">fwd.hpp:36</a></div></div>
<div class="ttc" id="a00281_html_ga5b83ae3d0fdec519c038e4d2cf967cf0"><div class="ttname"><a href="a00281.html#ga5b83ae3d0fdec519c038e4d2cf967cf0">glm::dvec3</a></div><div class="ttdeci">vec&lt; 3, double, defaultp &gt; dvec3</div><div class="ttdoc">3 components vector of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00202_source.html#l00015">vector_double3.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_ga119c41d73fe9977358174eb3ac1035a3"><div class="ttname"><a href="a00304.html#ga119c41d73fe9977358174eb3ac1035a3">glm::lowp_int8_t</a></div><div class="ttdeci">int8 lowp_int8_t</div><div class="ttdoc">Low qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00040">fwd.hpp:40</a></div></div>
<div class="ttc" id="a00303_html_gadb065dbe5c11271fef8cf2ea8608f187"><div class="ttname"><a href="a00303.html#gadb065dbe5c11271fef8cf2ea8608f187">glm::aligned_mat3x3</a></div><div class="ttdeci">aligned_highp_mat3x3 aligned_mat3x3</div><div class="ttdoc">3 by 3 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00994">gtc/type_aligned.hpp:994</a></div></div>
<div class="ttc" id="a00304_html_ga3e2e66ffbe341a80bc005ba2b9552110"><div class="ttname"><a href="a00304.html#ga3e2e66ffbe341a80bc005ba2b9552110">glm::f64mat4x3</a></div><div class="ttdeci">mat&lt; 4, 3, f64, defaultp &gt; f64mat4x3</div><div class="ttdoc">Double-qualifier floating-point 4x3 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00785">fwd.hpp:785</a></div></div>
<div class="ttc" id="a00304_html_gadb997e409103d4da18abd837e636a496"><div class="ttname"><a href="a00304.html#gadb997e409103d4da18abd837e636a496">glm::i64</a></div><div class="ttdeci">int64 i64</div><div class="ttdoc">64 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00076">fwd.hpp:76</a></div></div>
<div class="ttc" id="a00304_html_ga2a266e46ee218d0c680f12b35c500cc0"><div class="ttname"><a href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">glm::u32vec2</a></div><div class="ttdeci">vec&lt; 2, u32, defaultp &gt; u32vec2</div><div class="ttdoc">Default qualifier 32 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00380">fwd.hpp:380</a></div></div>
<div class="ttc" id="a00252_html_gab0b441adb4509bc58d2946c2239a8942"><div class="ttname"><a href="a00252.html#gab0b441adb4509bc58d2946c2239a8942">glm::quat</a></div><div class="ttdeci">qua&lt; float, defaultp &gt; quat</div><div class="ttdoc">Quaternion of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00131_source.html#l00035">quaternion_float.hpp:35</a></div></div>
<div class="ttc" id="a00304_html_ga5244cef85d6e870e240c76428a262ae8"><div class="ttname"><a href="a00304.html#ga5244cef85d6e870e240c76428a262ae8">glm::mediump_int32</a></div><div class="ttdeci">int32 mediump_int32</div><div class="ttdoc">Medium qualifier 32 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00065">fwd.hpp:65</a></div></div>
<div class="ttc" id="a00304_html_ga48310188e1d0c616bf8d78c92447523b"><div class="ttname"><a href="a00304.html#ga48310188e1d0c616bf8d78c92447523b">glm::i64vec2</a></div><div class="ttdeci">vec&lt; 2, i64, defaultp &gt; i64vec2</div><div class="ttdoc">64 bit signed integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00298">fwd.hpp:298</a></div></div>
<div class="ttc" id="a00304_html_ga3ab5fe184343d394fb6c2723c3ee3699"><div class="ttname"><a href="a00304.html#ga3ab5fe184343d394fb6c2723c3ee3699">glm::i16</a></div><div class="ttdeci">int16 i16</div><div class="ttdoc">16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00048">fwd.hpp:48</a></div></div>
<div class="ttc" id="a00281_html_ga57debab5d98ce618f7b2a97fe26eb3ac"><div class="ttname"><a href="a00281.html#ga57debab5d98ce618f7b2a97fe26eb3ac">glm::dvec4</a></div><div class="ttdeci">vec&lt; 4, double, defaultp &gt; dvec4</div><div class="ttdoc">4 components vector of double-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00204_source.html#l00015">vector_double4.hpp:15</a></div></div>
<div class="ttc" id="a00304_html_gad01cc6479bde1fd1870f13d3ed9530b3"><div class="ttname"><a href="a00304.html#gad01cc6479bde1fd1870f13d3ed9530b3">glm::fmat4x4</a></div><div class="ttdeci">mat&lt; 4, 4, f32, defaultp &gt; fmat4x4</div><div class="ttdoc">Single-qualifier floating-point 4x4 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00668">fwd.hpp:668</a></div></div>
<div class="ttc" id="a00283_html_ga8dd59e7fc6913ac5d61b86553e9148ba"><div class="ttname"><a href="a00283.html#ga8dd59e7fc6913ac5d61b86553e9148ba">glm::mat2</a></div><div class="ttdeci">mat&lt; 2, 2, float, defaultp &gt; mat2</div><div class="ttdoc">2 columns of 2 components matrix of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00082_source.html#l00020">matrix_float2x2.hpp:20</a></div></div>
<div class="ttc" id="a00303_html_ga43a92a24ca863e0e0f3b65834b3cf714"><div class="ttname"><a href="a00303.html#ga43a92a24ca863e0e0f3b65834b3cf714">glm::aligned_mat3</a></div><div class="ttdeci">aligned_highp_mat3 aligned_mat3</div><div class="ttdoc">3 by 3 matrix tightly aligned in memory of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l00967">gtc/type_aligned.hpp:967</a></div></div>
<div class="ttc" id="a00304_html_ga6af54d70d9beb0a7ef992a879e86b04f"><div class="ttname"><a href="a00304.html#ga6af54d70d9beb0a7ef992a879e86b04f">glm::fmat3x2</a></div><div class="ttdeci">mat&lt; 3, 2, f32, defaultp &gt; fmat3x2</div><div class="ttdoc">Single-qualifier floating-point 3x2 matrix. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00661">fwd.hpp:661</a></div></div>
<div class="ttc" id="a00304_html_ga529496d75775fb656a07993ea9af2450"><div class="ttname"><a href="a00304.html#ga529496d75775fb656a07993ea9af2450">glm::u16vec4</a></div><div class="ttdeci">vec&lt; 4, u16, defaultp &gt; u16vec4</div><div class="ttdoc">Default qualifier 16 bit unsigned integer vector of 4 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00362">fwd.hpp:362</a></div></div>
<div class="ttc" id="a00304_html_ga2a78447eb9d66a114b193f4a25899c16"><div class="ttname"><a href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">glm::u16vec2</a></div><div class="ttdeci">vec&lt; 2, u16, defaultp &gt; u16vec2</div><div class="ttdoc">Default qualifier 16 bit unsigned integer vector of 2 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00360">fwd.hpp:360</a></div></div>
<div class="ttc" id="a00304_html_gad1213a22bbb9e4107f07eaa4956f8281"><div class="ttname"><a href="a00304.html#gad1213a22bbb9e4107f07eaa4956f8281">glm::mediump_u8</a></div><div class="ttdeci">uint8 mediump_u8</div><div class="ttdoc">Medium qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00090">fwd.hpp:90</a></div></div>
<div class="ttc" id="a00303_html_ga18d859f87122b2b3b2992ffe86dbebc0"><div class="ttname"><a href="a00303.html#ga18d859f87122b2b3b2992ffe86dbebc0">glm::aligned_dvec2</a></div><div class="ttdeci">aligned_highp_dvec2 aligned_dvec2</div><div class="ttdoc">2 components vector aligned in memory of double-precision floating-point numbers. ...</div><div class="ttdef"><b>Definition:</b> <a href="a00161_source.html#l01111">gtc/type_aligned.hpp:1111</a></div></div>
<div class="ttc" id="a00304_html_ga80e72fe94c88498537e8158ba7591c54"><div class="ttname"><a href="a00304.html#ga80e72fe94c88498537e8158ba7591c54">glm::mediump_int16_t</a></div><div class="ttdeci">int16 mediump_int16_t</div><div class="ttdoc">Medium qualifier 16 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00055">fwd.hpp:55</a></div></div>
<div class="ttc" id="a00304_html_ga552a6bde5e75984efb0f863278da2e54"><div class="ttname"><a href="a00304.html#ga552a6bde5e75984efb0f863278da2e54">glm::lowp_i8</a></div><div class="ttdeci">int8 lowp_i8</div><div class="ttdoc">Low qualifier 8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00031">fwd.hpp:31</a></div></div>
<div class="ttc" id="a00304_html_ga667948cfe6fb3d6606c750729ec49f77"><div class="ttname"><a href="a00304.html#ga667948cfe6fb3d6606c750729ec49f77">glm::i64vec3</a></div><div class="ttdeci">vec&lt; 3, i64, defaultp &gt; i64vec3</div><div class="ttdoc">64 bit signed integer vector of 3 components type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00299">fwd.hpp:299</a></div></div>
<div class="ttc" id="a00283_html_gaefb0fc7a4960b782c18708bb6b655262"><div class="ttname"><a href="a00283.html#gaefb0fc7a4960b782c18708bb6b655262">glm::mat3</a></div><div class="ttdeci">mat&lt; 3, 3, float, defaultp &gt; mat3</div><div class="ttdoc">3 columns of 3 components matrix of single-precision floating-point numbers. </div><div class="ttdef"><b>Definition:</b> <a href="a00090_source.html#l00020">matrix_float3x3.hpp:20</a></div></div>
<div class="ttc" id="a00304_html_gacf54c3330ef60aa3d16cb676c7bcb8c7"><div class="ttname"><a href="a00304.html#gacf54c3330ef60aa3d16cb676c7bcb8c7">glm::highp_uint16_t</a></div><div class="ttdeci">uint16 highp_uint16_t</div><div class="ttdoc">High qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00114">fwd.hpp:114</a></div></div>
<div class="ttc" id="a00304_html_ga302ec977b0c0c3ea245b6c9275495355"><div class="ttname"><a href="a00304.html#ga302ec977b0c0c3ea245b6c9275495355">glm::i8</a></div><div class="ttdeci">int8 i8</div><div class="ttdoc">8 bit signed integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00034">fwd.hpp:34</a></div></div>
<div class="ttc" id="a00304_html_ga9b170dd4a8f38448a2dc93987c7875e9"><div class="ttname"><a href="a00304.html#ga9b170dd4a8f38448a2dc93987c7875e9">glm::mediump_uint64_t</a></div><div class="ttdeci">uint64 mediump_uint64_t</div><div class="ttdoc">Medium qualifier 64 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00141">fwd.hpp:141</a></div></div>
<div class="ttc" id="a00304_html_gadfe65c78231039e90507770db50c98c7"><div class="ttname"><a href="a00304.html#gadfe65c78231039e90507770db50c98c7">glm::mediump_uint8_t</a></div><div class="ttdeci">uint8 mediump_uint8_t</div><div class="ttdoc">Medium qualifier 8 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00099">fwd.hpp:99</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
<div class="ttc" id="a00304_html_ga2885a6c89916911e418c06bb76b9bdbb"><div class="ttname"><a href="a00304.html#ga2885a6c89916911e418c06bb76b9bdbb">glm::mediump_uint16</a></div><div class="ttdeci">uint16 mediump_uint16</div><div class="ttdoc">Medium qualifier 16 bit unsigned integer type. </div><div class="ttdef"><b>Definition:</b> <a href="a00035_source.html#l00109">fwd.hpp:109</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
