<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_color_space</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_color_space<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00013.html" title="GLM_GTX_color_space ">glm/gtx/color_space.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga789802bec2d4fe0f9741c731b4a8a7d8"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga789802bec2d4fe0f9741c731b4a8a7d8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00312.html#ga789802bec2d4fe0f9741c731b4a8a7d8">hsvColor</a> (vec&lt; 3, T, Q &gt; const &amp;rgbValue)</td></tr>
<tr class="memdesc:ga789802bec2d4fe0f9741c731b4a8a7d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a color from RGB color space to its color in HSV color space.  <a href="a00312.html#ga789802bec2d4fe0f9741c731b4a8a7d8">More...</a><br /></td></tr>
<tr class="separator:ga789802bec2d4fe0f9741c731b4a8a7d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad028e0a4f1a9c812b39439b746295b34"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad028e0a4f1a9c812b39439b746295b34"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00312.html#gad028e0a4f1a9c812b39439b746295b34">luminosity</a> (vec&lt; 3, T, Q &gt; const &amp;color)</td></tr>
<tr class="memdesc:gad028e0a4f1a9c812b39439b746295b34"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute color luminosity associating ratios (0.33, 0.59, 0.11) to RGB canals.  <a href="a00312.html#gad028e0a4f1a9c812b39439b746295b34">More...</a><br /></td></tr>
<tr class="separator:gad028e0a4f1a9c812b39439b746295b34"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5f9193be46f45f0655c05a0cdca006db"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5f9193be46f45f0655c05a0cdca006db"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00312.html#ga5f9193be46f45f0655c05a0cdca006db">rgbColor</a> (vec&lt; 3, T, Q &gt; const &amp;hsvValue)</td></tr>
<tr class="memdesc:ga5f9193be46f45f0655c05a0cdca006db"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a color from HSV color space to its color in RGB color space.  <a href="a00312.html#ga5f9193be46f45f0655c05a0cdca006db">More...</a><br /></td></tr>
<tr class="separator:ga5f9193be46f45f0655c05a0cdca006db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga01a97152b44e1550edcac60bd849e884"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga01a97152b44e1550edcac60bd849e884"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00312.html#ga01a97152b44e1550edcac60bd849e884">saturation</a> (T const s)</td></tr>
<tr class="memdesc:ga01a97152b44e1550edcac60bd849e884"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a saturation matrix.  <a href="a00312.html#ga01a97152b44e1550edcac60bd849e884">More...</a><br /></td></tr>
<tr class="separator:ga01a97152b44e1550edcac60bd849e884"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2156cea600e90148ece5bc96fd6db43a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2156cea600e90148ece5bc96fd6db43a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00312.html#ga2156cea600e90148ece5bc96fd6db43a">saturation</a> (T const s, vec&lt; 3, T, Q &gt; const &amp;color)</td></tr>
<tr class="memdesc:ga2156cea600e90148ece5bc96fd6db43a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modify the saturation of a color.  <a href="a00312.html#ga2156cea600e90148ece5bc96fd6db43a">More...</a><br /></td></tr>
<tr class="separator:ga2156cea600e90148ece5bc96fd6db43a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaba0eacee0736dae860e9371cc1ae4785"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaba0eacee0736dae860e9371cc1ae4785"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00312.html#gaba0eacee0736dae860e9371cc1ae4785">saturation</a> (T const s, vec&lt; 4, T, Q &gt; const &amp;color)</td></tr>
<tr class="memdesc:gaba0eacee0736dae860e9371cc1ae4785"><td class="mdescLeft">&#160;</td><td class="mdescRight">Modify the saturation of a color.  <a href="a00312.html#gaba0eacee0736dae860e9371cc1ae4785">More...</a><br /></td></tr>
<tr class="separator:gaba0eacee0736dae860e9371cc1ae4785"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00013.html" title="GLM_GTX_color_space ">glm/gtx/color_space.hpp</a>&gt; to use the features of this extension. </p>
<p>Related to RGB to HSV conversions and operations. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga789802bec2d4fe0f9741c731b4a8a7d8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::hsvColor </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>rgbValue</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a color from RGB color space to its color in HSV color space. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00312.html" title="Include <glm/gtx/color_space.hpp> to use the features of this extension. ">GLM_GTX_color_space</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad028e0a4f1a9c812b39439b746295b34"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::luminosity </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>color</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute color luminosity associating ratios (0.33, 0.59, 0.11) to RGB canals. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00312.html" title="Include <glm/gtx/color_space.hpp> to use the features of this extension. ">GLM_GTX_color_space</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5f9193be46f45f0655c05a0cdca006db"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::rgbColor </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>hsvValue</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a color from HSV color space to its color in RGB color space. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00312.html" title="Include <glm/gtx/color_space.hpp> to use the features of this extension. ">GLM_GTX_color_space</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga01a97152b44e1550edcac60bd849e884"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::saturation </td>
          <td>(</td>
          <td class="paramtype">T const&#160;</td>
          <td class="paramname"><em>s</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a saturation matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00312.html" title="Include <glm/gtx/color_space.hpp> to use the features of this extension. ">GLM_GTX_color_space</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2156cea600e90148ece5bc96fd6db43a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::saturation </td>
          <td>(</td>
          <td class="paramtype">T const&#160;</td>
          <td class="paramname"><em>s</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>color</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modify the saturation of a color. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00312.html" title="Include <glm/gtx/color_space.hpp> to use the features of this extension. ">GLM_GTX_color_space</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaba0eacee0736dae860e9371cc1ae4785"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, Q&gt; glm::saturation </td>
          <td>(</td>
          <td class="paramtype">T const&#160;</td>
          <td class="paramname"><em>s</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>color</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Modify the saturation of a color. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00312.html" title="Include <glm/gtx/color_space.hpp> to use the features of this extension. ">GLM_GTX_color_space</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
