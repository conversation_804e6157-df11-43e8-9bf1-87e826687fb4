<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: std_based_type.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">std_based_type.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00155.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00014"></a><span class="lineno">   14</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00015"></a><span class="lineno">   15</span>&#160;</div>
<div class="line"><a name="l00016"></a><span class="lineno">   16</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00017"></a><span class="lineno">   17</span>&#160;<span class="preprocessor">#include &quot;../glm.hpp&quot;</span></div>
<div class="line"><a name="l00018"></a><span class="lineno">   18</span>&#160;<span class="preprocessor">#include &lt;cstdlib&gt;</span></div>
<div class="line"><a name="l00019"></a><span class="lineno">   19</span>&#160;</div>
<div class="line"><a name="l00020"></a><span class="lineno">   20</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00021"></a><span class="lineno">   21</span>&#160;<span class="preprocessor">#       ifndef GLM_ENABLE_EXPERIMENTAL</span></div>
<div class="line"><a name="l00022"></a><span class="lineno">   22</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_std_based_type is an experimental extension and may change in the future. Use #define GLM_ENABLE_EXPERIMENTAL before including it, if you really want to use it.&quot;)</span></div>
<div class="line"><a name="l00023"></a><span class="lineno">   23</span>&#160;<span class="preprocessor">#       else</span></div>
<div class="line"><a name="l00024"></a><span class="lineno">   24</span>&#160;<span class="preprocessor">#               pragma message(&quot;GLM: GLM_GTX_std_based_type extension included&quot;)</span></div>
<div class="line"><a name="l00025"></a><span class="lineno">   25</span>&#160;<span class="preprocessor">#       endif</span></div>
<div class="line"><a name="l00026"></a><span class="lineno">   26</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00027"></a><span class="lineno">   27</span>&#160;</div>
<div class="line"><a name="l00028"></a><span class="lineno">   28</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00029"></a><span class="lineno">   29</span>&#160;{</div>
<div class="line"><a name="l00032"></a><span class="lineno">   32</span>&#160;</div>
<div class="line"><a name="l00035"></a><span class="lineno"><a class="line" href="a00359.html#gaeb877ac8f9a3703961736c1c5072cf68">   35</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, std::size_t, defaultp&gt;           <a class="code" href="a00359.html#gaeb877ac8f9a3703961736c1c5072cf68">size1</a>;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;</div>
<div class="line"><a name="l00039"></a><span class="lineno"><a class="line" href="a00359.html#ga1bfe8c4975ff282bce41be2bacd524fe">   39</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, std::size_t, defaultp&gt;           <a class="code" href="a00359.html#ga1bfe8c4975ff282bce41be2bacd524fe">size2</a>;</div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;</div>
<div class="line"><a name="l00043"></a><span class="lineno"><a class="line" href="a00359.html#gae1c72956d0359b0db332c6c8774d3b04">   43</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, std::size_t, defaultp&gt;           <a class="code" href="a00359.html#gae1c72956d0359b0db332c6c8774d3b04">size3</a>;</div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;</div>
<div class="line"><a name="l00047"></a><span class="lineno"><a class="line" href="a00359.html#ga3a19dde617beaf8ce3cfc2ac5064e9aa">   47</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, std::size_t, defaultp&gt;           <a class="code" href="a00359.html#ga3a19dde617beaf8ce3cfc2ac5064e9aa">size4</a>;</div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;</div>
<div class="line"><a name="l00051"></a><span class="lineno"><a class="line" href="a00359.html#gaaf6accc57f5aa50447ba7310ce3f0d6f">   51</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;1, std::size_t, defaultp&gt;           <a class="code" href="a00359.html#gaaf6accc57f5aa50447ba7310ce3f0d6f">size1_t</a>;</div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00055"></a><span class="lineno"><a class="line" href="a00359.html#ga5976c25657d4e2b5f73f39364c3845d6">   55</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;2, std::size_t, defaultp&gt;           <a class="code" href="a00359.html#ga5976c25657d4e2b5f73f39364c3845d6">size2_t</a>;</div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div>
<div class="line"><a name="l00059"></a><span class="lineno"><a class="line" href="a00359.html#gaf2654983c60d641fd3808e65a8dfad8d">   59</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;3, std::size_t, defaultp&gt;           <a class="code" href="a00359.html#gaf2654983c60d641fd3808e65a8dfad8d">size3_t</a>;</div>
<div class="line"><a name="l00060"></a><span class="lineno">   60</span>&#160;</div>
<div class="line"><a name="l00063"></a><span class="lineno"><a class="line" href="a00359.html#gaa423efcea63675a2df26990dbcb58656">   63</a></span>&#160;        <span class="keyword">typedef</span> vec&lt;4, std::size_t, defaultp&gt;           <a class="code" href="a00359.html#gaa423efcea63675a2df26990dbcb58656">size4_t</a>;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00067"></a><span class="lineno">   67</span>&#160;</div>
<div class="line"><a name="l00068"></a><span class="lineno">   68</span>&#160;<span class="preprocessor">#include &quot;std_based_type.inl&quot;</span></div>
<div class="ttc" id="a00359_html_gaeb877ac8f9a3703961736c1c5072cf68"><div class="ttname"><a href="a00359.html#gaeb877ac8f9a3703961736c1c5072cf68">glm::size1</a></div><div class="ttdeci">vec&lt; 1, std::size_t, defaultp &gt; size1</div><div class="ttdoc">Vector type based of one std::size_t component. </div><div class="ttdef"><b>Definition:</b> <a href="a00155_source.html#l00035">std_based_type.hpp:35</a></div></div>
<div class="ttc" id="a00359_html_gaf2654983c60d641fd3808e65a8dfad8d"><div class="ttname"><a href="a00359.html#gaf2654983c60d641fd3808e65a8dfad8d">glm::size3_t</a></div><div class="ttdeci">vec&lt; 3, std::size_t, defaultp &gt; size3_t</div><div class="ttdoc">Vector type based of three std::size_t components. </div><div class="ttdef"><b>Definition:</b> <a href="a00155_source.html#l00059">std_based_type.hpp:59</a></div></div>
<div class="ttc" id="a00359_html_ga5976c25657d4e2b5f73f39364c3845d6"><div class="ttname"><a href="a00359.html#ga5976c25657d4e2b5f73f39364c3845d6">glm::size2_t</a></div><div class="ttdeci">vec&lt; 2, std::size_t, defaultp &gt; size2_t</div><div class="ttdoc">Vector type based of two std::size_t components. </div><div class="ttdef"><b>Definition:</b> <a href="a00155_source.html#l00055">std_based_type.hpp:55</a></div></div>
<div class="ttc" id="a00359_html_ga3a19dde617beaf8ce3cfc2ac5064e9aa"><div class="ttname"><a href="a00359.html#ga3a19dde617beaf8ce3cfc2ac5064e9aa">glm::size4</a></div><div class="ttdeci">vec&lt; 4, std::size_t, defaultp &gt; size4</div><div class="ttdoc">Vector type based of four std::size_t components. </div><div class="ttdef"><b>Definition:</b> <a href="a00155_source.html#l00047">std_based_type.hpp:47</a></div></div>
<div class="ttc" id="a00359_html_gaaf6accc57f5aa50447ba7310ce3f0d6f"><div class="ttname"><a href="a00359.html#gaaf6accc57f5aa50447ba7310ce3f0d6f">glm::size1_t</a></div><div class="ttdeci">vec&lt; 1, std::size_t, defaultp &gt; size1_t</div><div class="ttdoc">Vector type based of one std::size_t component. </div><div class="ttdef"><b>Definition:</b> <a href="a00155_source.html#l00051">std_based_type.hpp:51</a></div></div>
<div class="ttc" id="a00359_html_gae1c72956d0359b0db332c6c8774d3b04"><div class="ttname"><a href="a00359.html#gae1c72956d0359b0db332c6c8774d3b04">glm::size3</a></div><div class="ttdeci">vec&lt; 3, std::size_t, defaultp &gt; size3</div><div class="ttdoc">Vector type based of three std::size_t components. </div><div class="ttdef"><b>Definition:</b> <a href="a00155_source.html#l00043">std_based_type.hpp:43</a></div></div>
<div class="ttc" id="a00359_html_ga1bfe8c4975ff282bce41be2bacd524fe"><div class="ttname"><a href="a00359.html#ga1bfe8c4975ff282bce41be2bacd524fe">glm::size2</a></div><div class="ttdeci">vec&lt; 2, std::size_t, defaultp &gt; size2</div><div class="ttdoc">Vector type based of two std::size_t components. </div><div class="ttdef"><b>Definition:</b> <a href="a00155_source.html#l00039">std_based_type.hpp:39</a></div></div>
<div class="ttc" id="a00359_html_gaa423efcea63675a2df26990dbcb58656"><div class="ttname"><a href="a00359.html#gaa423efcea63675a2df26990dbcb58656">glm::size4_t</a></div><div class="ttdeci">vec&lt; 4, std::size_t, defaultp &gt; size4_t</div><div class="ttdoc">Vector type based of four std::size_t components. </div><div class="ttdef"><b>Definition:</b> <a href="a00155_source.html#l00063">std_based_type.hpp:63</a></div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
