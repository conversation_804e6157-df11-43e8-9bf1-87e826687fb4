<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: vector_integer.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">vector_integer.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00274.html">GLM_EXT_vector_integer</a>  
<a href="#details">More...</a></p>

<p><a href="a00222_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaff61eca266da315002a3db92ff0dd604"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaff61eca266da315002a3db92ff0dd604"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, int, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00274.html#gaff61eca266da315002a3db92ff0dd604">findNSB</a> (vec&lt; L, T, Q &gt; const &amp;Source, vec&lt; L, int, Q &gt; SignificantBitCount)</td></tr>
<tr class="memdesc:gaff61eca266da315002a3db92ff0dd604"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the bit number of the Nth significant bit set to 1 in the binary representation of value.  <a href="a00274.html#gaff61eca266da315002a3db92ff0dd604">More...</a><br /></td></tr>
<tr class="separator:gaff61eca266da315002a3db92ff0dd604"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga354caf634ef333d9cb4844407416256a"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga354caf634ef333d9cb4844407416256a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00274.html#ga354caf634ef333d9cb4844407416256a">isMultiple</a> (vec&lt; L, T, Q &gt; const &amp;v, T Multiple)</td></tr>
<tr class="memdesc:ga354caf634ef333d9cb4844407416256a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return true if the 'Value' is a multiple of 'Multiple'.  <a href="a00274.html#ga354caf634ef333d9cb4844407416256a">More...</a><br /></td></tr>
<tr class="separator:ga354caf634ef333d9cb4844407416256a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabb4360e38c0943d8981ba965dead519d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gabb4360e38c0943d8981ba965dead519d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00274.html#gabb4360e38c0943d8981ba965dead519d">isMultiple</a> (vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)</td></tr>
<tr class="memdesc:gabb4360e38c0943d8981ba965dead519d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return true if the 'Value' is a multiple of 'Multiple'.  <a href="a00274.html#gabb4360e38c0943d8981ba965dead519d">More...</a><br /></td></tr>
<tr class="separator:gabb4360e38c0943d8981ba965dead519d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf2b61ded7049bcb13e25164f832a290"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gabf2b61ded7049bcb13e25164f832a290"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00274.html#gabf2b61ded7049bcb13e25164f832a290">isPowerOfTwo</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gabf2b61ded7049bcb13e25164f832a290"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return true if the value is a power of two number.  <a href="a00274.html#gabf2b61ded7049bcb13e25164f832a290">More...</a><br /></td></tr>
<tr class="separator:gabf2b61ded7049bcb13e25164f832a290"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gace38d00601cbf49cd4dc03f003ab42b7"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gace38d00601cbf49cd4dc03f003ab42b7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00274.html#gace38d00601cbf49cd4dc03f003ab42b7">nextMultiple</a> (vec&lt; L, T, Q &gt; const &amp;v, T Multiple)</td></tr>
<tr class="memdesc:gace38d00601cbf49cd4dc03f003ab42b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Higher multiple number of Source.  <a href="a00274.html#gace38d00601cbf49cd4dc03f003ab42b7">More...</a><br /></td></tr>
<tr class="separator:gace38d00601cbf49cd4dc03f003ab42b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacda365edad320c7aff19cc283a3b8ca2"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gacda365edad320c7aff19cc283a3b8ca2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00274.html#gacda365edad320c7aff19cc283a3b8ca2">nextMultiple</a> (vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)</td></tr>
<tr class="memdesc:gacda365edad320c7aff19cc283a3b8ca2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Higher multiple number of Source.  <a href="a00274.html#gacda365edad320c7aff19cc283a3b8ca2">More...</a><br /></td></tr>
<tr class="separator:gacda365edad320c7aff19cc283a3b8ca2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabba67f8aac9915e10fca727277274502"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gabba67f8aac9915e10fca727277274502"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00274.html#gabba67f8aac9915e10fca727277274502">nextPowerOfTwo</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gabba67f8aac9915e10fca727277274502"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just higher the input value, round up to a power of two.  <a href="a00274.html#gabba67f8aac9915e10fca727277274502">More...</a><br /></td></tr>
<tr class="separator:gabba67f8aac9915e10fca727277274502"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b3915a7cd3d50ff4976ab7a75a6880a"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga7b3915a7cd3d50ff4976ab7a75a6880a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00274.html#ga7b3915a7cd3d50ff4976ab7a75a6880a">prevMultiple</a> (vec&lt; L, T, Q &gt; const &amp;v, T Multiple)</td></tr>
<tr class="memdesc:ga7b3915a7cd3d50ff4976ab7a75a6880a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lower multiple number of Source.  <a href="a00274.html#ga7b3915a7cd3d50ff4976ab7a75a6880a">More...</a><br /></td></tr>
<tr class="separator:ga7b3915a7cd3d50ff4976ab7a75a6880a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga51e04379e8aebbf83e2e5ab094578ee9"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga51e04379e8aebbf83e2e5ab094578ee9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00274.html#ga51e04379e8aebbf83e2e5ab094578ee9">prevMultiple</a> (vec&lt; L, T, Q &gt; const &amp;v, vec&lt; L, T, Q &gt; const &amp;Multiple)</td></tr>
<tr class="memdesc:ga51e04379e8aebbf83e2e5ab094578ee9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lower multiple number of Source.  <a href="a00274.html#ga51e04379e8aebbf83e2e5ab094578ee9">More...</a><br /></td></tr>
<tr class="separator:ga51e04379e8aebbf83e2e5ab094578ee9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga759db73f14d79f63612bd2398b577e7a"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga759db73f14d79f63612bd2398b577e7a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00274.html#ga759db73f14d79f63612bd2398b577e7a">prevPowerOfTwo</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga759db73f14d79f63612bd2398b577e7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just lower the input value, round down to a power of two.  <a href="a00274.html#ga759db73f14d79f63612bd2398b577e7a">More...</a><br /></td></tr>
<tr class="separator:ga759db73f14d79f63612bd2398b577e7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00274.html">GLM_EXT_vector_integer</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00280.html" title="Features that implement in C++ the GLSL specification as closely as possible. ">Core features</a> (dependence) </dd>
<dd>
<a class="el" href="a00274.html" title="Include <glm/ext/vector_integer.hpp> to use the features of this extension. ">GLM_EXT_vector_integer</a> (dependence) </dd></dl>

<p>Definition in file <a class="el" href="a00222_source.html">vector_integer.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
