<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: gtx Directory Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">gtx Directory Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="files"></a>
Files</h2></td></tr>
<tr class="memitem:a00007"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00007.html">associated_min_max.hpp</a> <a href="a00007_source.html">[code]</a></td></tr>
<tr class="memdesc:a00007"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00308.html">GLM_GTX_associated_min_max</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00008"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00008.html">bit.hpp</a> <a href="a00008_source.html">[code]</a></td></tr>
<tr class="memdesc:a00008"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00309.html">GLM_GTX_bit</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00010"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00010.html">closest_point.hpp</a> <a href="a00010_source.html">[code]</a></td></tr>
<tr class="memdesc:a00010"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00310.html">GLM_GTX_closest_point</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00011"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00011.html">color_encoding.hpp</a> <a href="a00011_source.html">[code]</a></td></tr>
<tr class="memdesc:a00011"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00311.html">GLM_GTX_color_encoding</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00013"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00013.html">gtx/color_space.hpp</a> <a href="a00013_source.html">[code]</a></td></tr>
<tr class="memdesc:a00013"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00312.html">GLM_GTX_color_space</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00014"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00014.html">color_space_YCoCg.hpp</a> <a href="a00014_source.html">[code]</a></td></tr>
<tr class="memdesc:a00014"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00313.html">GLM_GTX_color_space_YCoCg</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00016"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00016.html">gtx/common.hpp</a> <a href="a00016_source.html">[code]</a></td></tr>
<tr class="memdesc:a00016"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00314.html">GLM_GTX_common</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00017"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00017.html">compatibility.hpp</a> <a href="a00017_source.html">[code]</a></td></tr>
<tr class="memdesc:a00017"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00315.html">GLM_GTX_compatibility</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00018"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00018.html">component_wise.hpp</a> <a href="a00018_source.html">[code]</a></td></tr>
<tr class="memdesc:a00018"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00316.html">GLM_GTX_component_wise</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00022"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00022.html">dual_quaternion.hpp</a> <a href="a00022_source.html">[code]</a></td></tr>
<tr class="memdesc:a00022"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00317.html">GLM_GTX_dual_quaternion</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00023"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00023.html">easing.hpp</a> <a href="a00023_source.html">[code]</a></td></tr>
<tr class="memdesc:a00023"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00318.html">GLM_GTX_easing</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00025"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00025.html">euler_angles.hpp</a> <a href="a00025_source.html">[code]</a></td></tr>
<tr class="memdesc:a00025"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00319.html">GLM_GTX_euler_angles</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00028"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00028.html">extend.hpp</a> <a href="a00028_source.html">[code]</a></td></tr>
<tr class="memdesc:a00028"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00320.html">GLM_GTX_extend</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00029"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00029.html">extended_min_max.hpp</a> <a href="a00029_source.html">[code]</a></td></tr>
<tr class="memdesc:a00029"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00321.html">GLM_GTX_extented_min_max</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00030"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00030.html">exterior_product.hpp</a> <a href="a00030_source.html">[code]</a></td></tr>
<tr class="memdesc:a00030"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00322.html">GLM_GTX_exterior_product</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00031"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00031.html">fast_exponential.hpp</a> <a href="a00031_source.html">[code]</a></td></tr>
<tr class="memdesc:a00031"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00323.html">GLM_GTX_fast_exponential</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00032"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00032.html">fast_square_root.hpp</a> <a href="a00032_source.html">[code]</a></td></tr>
<tr class="memdesc:a00032"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00324.html">GLM_GTX_fast_square_root</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00033"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00033.html">fast_trigonometry.hpp</a> <a href="a00033_source.html">[code]</a></td></tr>
<tr class="memdesc:a00033"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00325.html">GLM_GTX_fast_trigonometry</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00034"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00034.html">functions.hpp</a> <a href="a00034_source.html">[code]</a></td></tr>
<tr class="memdesc:a00034"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00326.html">GLM_GTX_functions</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00038"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00038.html">gradient_paint.hpp</a> <a href="a00038_source.html">[code]</a></td></tr>
<tr class="memdesc:a00038"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00327.html">GLM_GTX_gradient_paint</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00039"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00039.html">handed_coordinate_space.hpp</a> <a href="a00039_source.html">[code]</a></td></tr>
<tr class="memdesc:a00039"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00328.html">GLM_GTX_handed_coordinate_space</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00040"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00040.html">hash.hpp</a> <a href="a00040_source.html">[code]</a></td></tr>
<tr class="memdesc:a00040"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00329.html">GLM_GTX_hash</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00042"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00042.html">gtx/integer.hpp</a> <a href="a00042_source.html">[code]</a></td></tr>
<tr class="memdesc:a00042"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00330.html">GLM_GTX_integer</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00044"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00044.html">intersect.hpp</a> <a href="a00044_source.html">[code]</a></td></tr>
<tr class="memdesc:a00044"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00331.html">GLM_GTX_intersect</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00045"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00045.html">io.hpp</a> <a href="a00045_source.html">[code]</a></td></tr>
<tr class="memdesc:a00045"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00332.html">GLM_GTX_io</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00046"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00046.html">log_base.hpp</a> <a href="a00046_source.html">[code]</a></td></tr>
<tr class="memdesc:a00046"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00333.html">GLM_GTX_log_base</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00061"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00061.html">matrix_cross_product.hpp</a> <a href="a00061_source.html">[code]</a></td></tr>
<tr class="memdesc:a00061"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00334.html">GLM_GTX_matrix_cross_product</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00062"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00062.html">matrix_decompose.hpp</a> <a href="a00062_source.html">[code]</a></td></tr>
<tr class="memdesc:a00062"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00335.html">GLM_GTX_matrix_decompose</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00081"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00081.html">matrix_factorisation.hpp</a> <a href="a00081_source.html">[code]</a></td></tr>
<tr class="memdesc:a00081"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00336.html">GLM_GTX_matrix_factorisation</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00101"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00101.html">matrix_interpolation.hpp</a> <a href="a00101_source.html">[code]</a></td></tr>
<tr class="memdesc:a00101"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00337.html">GLM_GTX_matrix_interpolation</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00103"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00103.html">matrix_major_storage.hpp</a> <a href="a00103_source.html">[code]</a></td></tr>
<tr class="memdesc:a00103"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00338.html">GLM_GTX_matrix_major_storage</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00104"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00104.html">matrix_operation.hpp</a> <a href="a00104_source.html">[code]</a></td></tr>
<tr class="memdesc:a00104"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00339.html">GLM_GTX_matrix_operation</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00106"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00106.html">matrix_query.hpp</a> <a href="a00106_source.html">[code]</a></td></tr>
<tr class="memdesc:a00106"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00340.html">GLM_GTX_matrix_query</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00110"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00110.html">matrix_transform_2d.hpp</a> <a href="a00110_source.html">[code]</a></td></tr>
<tr class="memdesc:a00110"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00341.html">GLM_GTX_matrix_transform_2d</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00111"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00111.html">mixed_product.hpp</a> <a href="a00111_source.html">[code]</a></td></tr>
<tr class="memdesc:a00111"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00342.html">GLM_GTX_mixed_producte</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00113"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00113.html">norm.hpp</a> <a href="a00113_source.html">[code]</a></td></tr>
<tr class="memdesc:a00113"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00343.html">GLM_GTX_norm</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00114"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00114.html">normal.hpp</a> <a href="a00114_source.html">[code]</a></td></tr>
<tr class="memdesc:a00114"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00344.html">GLM_GTX_normal</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00115"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00115.html">normalize_dot.hpp</a> <a href="a00115_source.html">[code]</a></td></tr>
<tr class="memdesc:a00115"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00345.html">GLM_GTX_normalize_dot</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00116"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00116.html">number_precision.hpp</a> <a href="a00116_source.html">[code]</a></td></tr>
<tr class="memdesc:a00116"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00346.html">GLM_GTX_number_precision</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00117"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00117.html">optimum_pow.hpp</a> <a href="a00117_source.html">[code]</a></td></tr>
<tr class="memdesc:a00117"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00347.html">GLM_GTX_optimum_pow</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00118"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00118.html">orthonormalize.hpp</a> <a href="a00118_source.html">[code]</a></td></tr>
<tr class="memdesc:a00118"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00348.html">GLM_GTX_orthonormalize</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00121"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00121.html">perpendicular.hpp</a> <a href="a00121_source.html">[code]</a></td></tr>
<tr class="memdesc:a00121"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00349.html">GLM_GTX_perpendicular</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00122"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00122.html">polar_coordinates.hpp</a> <a href="a00122_source.html">[code]</a></td></tr>
<tr class="memdesc:a00122"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00350.html">GLM_GTX_polar_coordinates</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00123"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00123.html">projection.hpp</a> <a href="a00123_source.html">[code]</a></td></tr>
<tr class="memdesc:a00123"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00351.html">GLM_GTX_projection</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00126"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00126.html">gtx/quaternion.hpp</a> <a href="a00126_source.html">[code]</a></td></tr>
<tr class="memdesc:a00126"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00352.html">GLM_GTX_quaternion</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00138"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00138.html">range.hpp</a> <a href="a00138_source.html">[code]</a></td></tr>
<tr class="memdesc:a00138"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00353.html">GLM_GTX_range</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00139"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00139.html">raw_data.hpp</a> <a href="a00139_source.html">[code]</a></td></tr>
<tr class="memdesc:a00139"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00354.html">GLM_GTX_raw_data</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00141"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00141.html">rotate_normalized_axis.hpp</a> <a href="a00141_source.html">[code]</a></td></tr>
<tr class="memdesc:a00141"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00355.html">GLM_GTX_rotate_normalized_axis</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00142"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00142.html">rotate_vector.hpp</a> <a href="a00142_source.html">[code]</a></td></tr>
<tr class="memdesc:a00142"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00356.html">GLM_GTX_rotate_vector</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00148"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00148.html">scalar_multiplication.hpp</a> <a href="a00148_source.html">[code]</a></td></tr>
<tr class="memdesc:a00148"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00287.html">Experimental extensions</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00150"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00150.html">gtx/scalar_relational.hpp</a> <a href="a00150_source.html">[code]</a></td></tr>
<tr class="memdesc:a00150"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00357.html">GLM_GTX_scalar_relational</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00154"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00154.html">spline.hpp</a> <a href="a00154_source.html">[code]</a></td></tr>
<tr class="memdesc:a00154"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00358.html">GLM_GTX_spline</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00155"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00155.html">std_based_type.hpp</a> <a href="a00155_source.html">[code]</a></td></tr>
<tr class="memdesc:a00155"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00359.html">GLM_GTX_std_based_type</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00156"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00156.html">string_cast.hpp</a> <a href="a00156_source.html">[code]</a></td></tr>
<tr class="memdesc:a00156"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00360.html">GLM_GTX_string_cast</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00157"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00157.html">texture.hpp</a> <a href="a00157_source.html">[code]</a></td></tr>
<tr class="memdesc:a00157"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00361.html">GLM_GTX_texture</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00158"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00158.html">transform.hpp</a> <a href="a00158_source.html">[code]</a></td></tr>
<tr class="memdesc:a00158"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00362.html">GLM_GTX_transform</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00159"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00159.html">transform2.hpp</a> <a href="a00159_source.html">[code]</a></td></tr>
<tr class="memdesc:a00159"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00363.html">GLM_GTX_transform2</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00162"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00162.html">gtx/type_aligned.hpp</a> <a href="a00162_source.html">[code]</a></td></tr>
<tr class="memdesc:a00162"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00364.html">GLM_GTX_type_aligned</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00177"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00177.html">type_trait.hpp</a> <a href="a00177_source.html">[code]</a></td></tr>
<tr class="memdesc:a00177"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00365.html">GLM_GTX_type_trait</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00187"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00187.html">vec_swizzle.hpp</a> <a href="a00187_source.html">[code]</a></td></tr>
<tr class="memdesc:a00187"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00366.html">GLM_GTX_vec_swizzle</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00188"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00188.html">vector_angle.hpp</a> <a href="a00188_source.html">[code]</a></td></tr>
<tr class="memdesc:a00188"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00367.html">GLM_GTX_vector_angle</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00223"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00223.html">vector_query.hpp</a> <a href="a00223_source.html">[code]</a></td></tr>
<tr class="memdesc:a00223"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00368.html">GLM_GTX_vector_query</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00235"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00235.html">wrap.hpp</a> <a href="a00235_source.html">[code]</a></td></tr>
<tr class="memdesc:a00235"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00369.html">GLM_GTX_wrap</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
