<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_packing</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_packing<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00119.html" title="GLM_GTC_packing ">glm/gtc/packing.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga4944ad465ff950e926d49621f916c78d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga4944ad465ff950e926d49621f916c78d">packF2x11_1x10</a> (vec3 const &amp;v)</td></tr>
<tr class="memdesc:ga4944ad465ff950e926d49621f916c78d"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the first two components of the normalized floating-point value v into 11-bit signless floating-point values.  <a href="a00298.html#ga4944ad465ff950e926d49621f916c78d">More...</a><br /></td></tr>
<tr class="separator:ga4944ad465ff950e926d49621f916c78d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3f648fc205467792dc6d8c59c748f8a6"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga3f648fc205467792dc6d8c59c748f8a6">packF3x9_E1x5</a> (vec3 const &amp;v)</td></tr>
<tr class="memdesc:ga3f648fc205467792dc6d8c59c748f8a6"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the first two components of the normalized floating-point value v into 11-bit signless floating-point values.  <a href="a00298.html#ga3f648fc205467792dc6d8c59c748f8a6">More...</a><br /></td></tr>
<tr class="separator:ga3f648fc205467792dc6d8c59c748f8a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2d8bbce673ebc04831c1fb05c47f5251"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:ga2d8bbce673ebc04831c1fb05c47f5251"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, uint16, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#ga2d8bbce673ebc04831c1fb05c47f5251">packHalf</a> (vec&lt; L, float, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga2d8bbce673ebc04831c1fb05c47f5251"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer vector obtained by converting the components of a floating-point vector to the 16-bit floating-point representation found in the OpenGL Specification.  <a href="a00298.html#ga2d8bbce673ebc04831c1fb05c47f5251">More...</a><br /></td></tr>
<tr class="separator:ga2d8bbce673ebc04831c1fb05c47f5251"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43f2093b6ff192a79058ff7834fc3528"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga43f2093b6ff192a79058ff7834fc3528">packHalf1x16</a> (float v)</td></tr>
<tr class="memdesc:ga43f2093b6ff192a79058ff7834fc3528"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer obtained by converting the components of a floating-point scalar to the 16-bit floating-point representation found in the OpenGL Specification, and then packing this 16-bit value into a 16-bit unsigned integer.  <a href="a00298.html#ga43f2093b6ff192a79058ff7834fc3528">More...</a><br /></td></tr>
<tr class="separator:ga43f2093b6ff192a79058ff7834fc3528"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafe2f7b39caf8f5ec555e1c059ec530e6"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gafe2f7b39caf8f5ec555e1c059ec530e6">packHalf4x16</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:gafe2f7b39caf8f5ec555e1c059ec530e6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer obtained by converting the components of a four-component floating-point vector to the 16-bit floating-point representation found in the OpenGL Specification, and then packing these four 16-bit values into a 64-bit unsigned integer.  <a href="a00298.html#gafe2f7b39caf8f5ec555e1c059ec530e6">More...</a><br /></td></tr>
<tr class="separator:gafe2f7b39caf8f5ec555e1c059ec530e6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga06ecb6afb902dba45419008171db9023"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga06ecb6afb902dba45419008171db9023">packI3x10_1x2</a> (ivec4 const &amp;v)</td></tr>
<tr class="memdesc:ga06ecb6afb902dba45419008171db9023"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer obtained by converting the components of a four-component signed integer vector to the 10-10-10-2-bit signed integer representation found in the OpenGL Specification, and then packing these four values into a 32-bit unsigned integer.  <a href="a00298.html#ga06ecb6afb902dba45419008171db9023">More...</a><br /></td></tr>
<tr class="separator:ga06ecb6afb902dba45419008171db9023"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3644163cf3a47bf1d4af1f4b03013a7e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga3644163cf3a47bf1d4af1f4b03013a7e">packInt2x16</a> (i16vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga3644163cf3a47bf1d4af1f4b03013a7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed integer.  <a href="a00298.html#ga3644163cf3a47bf1d4af1f4b03013a7e">More...</a><br /></td></tr>
<tr class="separator:ga3644163cf3a47bf1d4af1f4b03013a7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1e4c8a9e67d86b61a6eec86703a827a"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gad1e4c8a9e67d86b61a6eec86703a827a">packInt2x32</a> (i32vec2 const &amp;v)</td></tr>
<tr class="memdesc:gad1e4c8a9e67d86b61a6eec86703a827a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed integer.  <a href="a00298.html#gad1e4c8a9e67d86b61a6eec86703a827a">More...</a><br /></td></tr>
<tr class="separator:gad1e4c8a9e67d86b61a6eec86703a827a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8884b1f2292414f36d59ef3be5d62914"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga8884b1f2292414f36d59ef3be5d62914">packInt2x8</a> (i8vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga8884b1f2292414f36d59ef3be5d62914"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed integer.  <a href="a00298.html#ga8884b1f2292414f36d59ef3be5d62914">More...</a><br /></td></tr>
<tr class="separator:ga8884b1f2292414f36d59ef3be5d62914"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1989f093a27ae69cf9207145be48b3d7"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga1989f093a27ae69cf9207145be48b3d7">packInt4x16</a> (i16vec4 const &amp;v)</td></tr>
<tr class="memdesc:ga1989f093a27ae69cf9207145be48b3d7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed integer.  <a href="a00298.html#ga1989f093a27ae69cf9207145be48b3d7">More...</a><br /></td></tr>
<tr class="separator:ga1989f093a27ae69cf9207145be48b3d7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2238401d5ce2aaade1a44ba19709072"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaf2238401d5ce2aaade1a44ba19709072">packInt4x8</a> (i8vec4 const &amp;v)</td></tr>
<tr class="memdesc:gaf2238401d5ce2aaade1a44ba19709072"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed integer.  <a href="a00298.html#gaf2238401d5ce2aaade1a44ba19709072">More...</a><br /></td></tr>
<tr class="separator:gaf2238401d5ce2aaade1a44ba19709072"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0466daf4c90f76cc64b3f105ce727295"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0466daf4c90f76cc64b3f105ce727295"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#ga0466daf4c90f76cc64b3f105ce727295">packRGBM</a> (vec&lt; 3, T, Q &gt; const &amp;rgb)</td></tr>
<tr class="memdesc:ga0466daf4c90f76cc64b3f105ce727295"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer vector obtained by converting the components of a floating-point vector to the 16-bit floating-point representation found in the OpenGL Specification.  <a href="a00298.html#ga0466daf4c90f76cc64b3f105ce727295">More...</a><br /></td></tr>
<tr class="separator:ga0466daf4c90f76cc64b3f105ce727295"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa54b5855a750d6aeb12c1c902f5939b8"><td class="memTemplParams" colspan="2">template&lt;typename intType , length_t L, typename floatType , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa54b5855a750d6aeb12c1c902f5939b8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, intType, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#gaa54b5855a750d6aeb12c1c902f5939b8">packSnorm</a> (vec&lt; L, floatType, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaa54b5855a750d6aeb12c1c902f5939b8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into signed integer values.  <a href="a00298.html#gaa54b5855a750d6aeb12c1c902f5939b8">More...</a><br /></td></tr>
<tr class="separator:gaa54b5855a750d6aeb12c1c902f5939b8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab22f8bcfdb5fc65af4701b25f143c1af"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab22f8bcfdb5fc65af4701b25f143c1af">packSnorm1x16</a> (float v)</td></tr>
<tr class="memdesc:gab22f8bcfdb5fc65af4701b25f143c1af"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the normalized floating-point value v into 16-bit integer value.  <a href="a00298.html#gab22f8bcfdb5fc65af4701b25f143c1af">More...</a><br /></td></tr>
<tr class="separator:gab22f8bcfdb5fc65af4701b25f143c1af"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae3592e0795e62aaa1865b3a10496a7a1"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint8&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gae3592e0795e62aaa1865b3a10496a7a1">packSnorm1x8</a> (float s)</td></tr>
<tr class="memdesc:gae3592e0795e62aaa1865b3a10496a7a1"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the normalized floating-point value v into 8-bit integer value.  <a href="a00298.html#gae3592e0795e62aaa1865b3a10496a7a1">More...</a><br /></td></tr>
<tr class="separator:gae3592e0795e62aaa1865b3a10496a7a1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6be3cfb2cce3702f03e91bbeb5286d7e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga6be3cfb2cce3702f03e91bbeb5286d7e">packSnorm2x8</a> (vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga6be3cfb2cce3702f03e91bbeb5286d7e"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 8-bit integer values.  <a href="a00298.html#ga6be3cfb2cce3702f03e91bbeb5286d7e">More...</a><br /></td></tr>
<tr class="separator:ga6be3cfb2cce3702f03e91bbeb5286d7e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab997545661877d2c7362a5084d3897d3"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab997545661877d2c7362a5084d3897d3">packSnorm3x10_1x2</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:gab997545661877d2c7362a5084d3897d3"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the first three components of the normalized floating-point value v into 10-bit signed integer values.  <a href="a00298.html#gab997545661877d2c7362a5084d3897d3">More...</a><br /></td></tr>
<tr class="separator:gab997545661877d2c7362a5084d3897d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga358943934d21da947d5bcc88c2ab7832"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga358943934d21da947d5bcc88c2ab7832">packSnorm4x16</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:ga358943934d21da947d5bcc88c2ab7832"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 16-bit integer values.  <a href="a00298.html#ga358943934d21da947d5bcc88c2ab7832">More...</a><br /></td></tr>
<tr class="separator:ga358943934d21da947d5bcc88c2ab7832"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada3d88d59f0f458f9c51a9fd359a4bc0"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gada3d88d59f0f458f9c51a9fd359a4bc0">packU3x10_1x2</a> (uvec4 const &amp;v)</td></tr>
<tr class="memdesc:gada3d88d59f0f458f9c51a9fd359a4bc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer obtained by converting the components of a four-component unsigned integer vector to the 10-10-10-2-bit unsigned integer representation found in the OpenGL Specification, and then packing these four values into a 32-bit unsigned integer.  <a href="a00298.html#gada3d88d59f0f458f9c51a9fd359a4bc0">More...</a><br /></td></tr>
<tr class="separator:gada3d88d59f0f458f9c51a9fd359a4bc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5eecc9e8cbaf51ac6cf57501e670ee19"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga5eecc9e8cbaf51ac6cf57501e670ee19">packUint2x16</a> (u16vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga5eecc9e8cbaf51ac6cf57501e670ee19"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed unsigned integer.  <a href="a00298.html#ga5eecc9e8cbaf51ac6cf57501e670ee19">More...</a><br /></td></tr>
<tr class="separator:ga5eecc9e8cbaf51ac6cf57501e670ee19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa864081097b86e83d8e4a4d79c382b22"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaa864081097b86e83d8e4a4d79c382b22">packUint2x32</a> (u32vec2 const &amp;v)</td></tr>
<tr class="memdesc:gaa864081097b86e83d8e4a4d79c382b22"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed unsigned integer.  <a href="a00298.html#gaa864081097b86e83d8e4a4d79c382b22">More...</a><br /></td></tr>
<tr class="separator:gaa864081097b86e83d8e4a4d79c382b22"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3c3c9fb53ae7823b10fa083909357590"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga3c3c9fb53ae7823b10fa083909357590">packUint2x8</a> (u8vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga3c3c9fb53ae7823b10fa083909357590"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed unsigned integer.  <a href="a00298.html#ga3c3c9fb53ae7823b10fa083909357590">More...</a><br /></td></tr>
<tr class="separator:ga3c3c9fb53ae7823b10fa083909357590"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2ceb62cca347d8ace42ee90317a3f1f9"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga2ceb62cca347d8ace42ee90317a3f1f9">packUint4x16</a> (u16vec4 const &amp;v)</td></tr>
<tr class="memdesc:ga2ceb62cca347d8ace42ee90317a3f1f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed unsigned integer.  <a href="a00298.html#ga2ceb62cca347d8ace42ee90317a3f1f9">More...</a><br /></td></tr>
<tr class="separator:ga2ceb62cca347d8ace42ee90317a3f1f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa0fe2f09aeb403cd66c1a062f58861ab"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaa0fe2f09aeb403cd66c1a062f58861ab">packUint4x8</a> (u8vec4 const &amp;v)</td></tr>
<tr class="memdesc:gaa0fe2f09aeb403cd66c1a062f58861ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component from an integer vector into a packed unsigned integer.  <a href="a00298.html#gaa0fe2f09aeb403cd66c1a062f58861ab">More...</a><br /></td></tr>
<tr class="separator:gaa0fe2f09aeb403cd66c1a062f58861ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaccd3f27e6ba5163eb7aa9bc8ff96251a"><td class="memTemplParams" colspan="2">template&lt;typename uintType , length_t L, typename floatType , qualifier Q&gt; </td></tr>
<tr class="memitem:gaccd3f27e6ba5163eb7aa9bc8ff96251a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, uintType, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#gaccd3f27e6ba5163eb7aa9bc8ff96251a">packUnorm</a> (vec&lt; L, floatType, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gaccd3f27e6ba5163eb7aa9bc8ff96251a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into unsigned integer values.  <a href="a00298.html#gaccd3f27e6ba5163eb7aa9bc8ff96251a">More...</a><br /></td></tr>
<tr class="separator:gaccd3f27e6ba5163eb7aa9bc8ff96251a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f82737bf2a44bedff1d286b76837886"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga9f82737bf2a44bedff1d286b76837886">packUnorm1x16</a> (float v)</td></tr>
<tr class="memdesc:ga9f82737bf2a44bedff1d286b76837886"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the normalized floating-point value v into a 16-bit integer value.  <a href="a00298.html#ga9f82737bf2a44bedff1d286b76837886">More...</a><br /></td></tr>
<tr class="separator:ga9f82737bf2a44bedff1d286b76837886"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga768e0337dd6246773f14aa0a421fe9a8"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga768e0337dd6246773f14aa0a421fe9a8">packUnorm1x5_1x6_1x5</a> (vec3 const &amp;v)</td></tr>
<tr class="memdesc:ga768e0337dd6246773f14aa0a421fe9a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into unsigned integer values.  <a href="a00298.html#ga768e0337dd6246773f14aa0a421fe9a8">More...</a><br /></td></tr>
<tr class="separator:ga768e0337dd6246773f14aa0a421fe9a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4b2fa60df3460403817d28b082ee0736"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint8&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga4b2fa60df3460403817d28b082ee0736">packUnorm1x8</a> (float v)</td></tr>
<tr class="memdesc:ga4b2fa60df3460403817d28b082ee0736"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the normalized floating-point value v into a 8-bit integer value.  <a href="a00298.html#ga4b2fa60df3460403817d28b082ee0736">More...</a><br /></td></tr>
<tr class="separator:ga4b2fa60df3460403817d28b082ee0736"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f9abdb50f9be1aa1c14912504a0d98d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint8&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga7f9abdb50f9be1aa1c14912504a0d98d">packUnorm2x3_1x2</a> (vec3 const &amp;v)</td></tr>
<tr class="memdesc:ga7f9abdb50f9be1aa1c14912504a0d98d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into unsigned integer values.  <a href="a00298.html#ga7f9abdb50f9be1aa1c14912504a0d98d">More...</a><br /></td></tr>
<tr class="separator:ga7f9abdb50f9be1aa1c14912504a0d98d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab6bbd5be3b8e6db538ecb33a7844481c"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint8&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab6bbd5be3b8e6db538ecb33a7844481c">packUnorm2x4</a> (vec2 const &amp;v)</td></tr>
<tr class="memdesc:gab6bbd5be3b8e6db538ecb33a7844481c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into unsigned integer values.  <a href="a00298.html#gab6bbd5be3b8e6db538ecb33a7844481c">More...</a><br /></td></tr>
<tr class="separator:gab6bbd5be3b8e6db538ecb33a7844481c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9a666b1c688ab54100061ed06526de6e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga9a666b1c688ab54100061ed06526de6e">packUnorm2x8</a> (vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga9a666b1c688ab54100061ed06526de6e"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 8-bit integer values.  <a href="a00298.html#ga9a666b1c688ab54100061ed06526de6e">More...</a><br /></td></tr>
<tr class="separator:ga9a666b1c688ab54100061ed06526de6e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a1ee625d2707c60530fb3fca2980b19"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga8a1ee625d2707c60530fb3fca2980b19">packUnorm3x10_1x2</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:ga8a1ee625d2707c60530fb3fca2980b19"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts the first three components of the normalized floating-point value v into 10-bit unsigned integer values.  <a href="a00298.html#ga8a1ee625d2707c60530fb3fca2980b19">More...</a><br /></td></tr>
<tr class="separator:ga8a1ee625d2707c60530fb3fca2980b19"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec4112086d7fb133bea104a7c237de52"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaec4112086d7fb133bea104a7c237de52">packUnorm3x5_1x1</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:gaec4112086d7fb133bea104a7c237de52"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into unsigned integer values.  <a href="a00298.html#gaec4112086d7fb133bea104a7c237de52">More...</a><br /></td></tr>
<tr class="separator:gaec4112086d7fb133bea104a7c237de52"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1f63c264e7ab63264e2b2a99fd393897"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga1f63c264e7ab63264e2b2a99fd393897">packUnorm4x16</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:ga1f63c264e7ab63264e2b2a99fd393897"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 16-bit integer values.  <a href="a00298.html#ga1f63c264e7ab63264e2b2a99fd393897">More...</a><br /></td></tr>
<tr class="separator:ga1f63c264e7ab63264e2b2a99fd393897"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad3e7e3ce521513584a53aedc5f9765c1"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gad3e7e3ce521513584a53aedc5f9765c1">packUnorm4x4</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:gad3e7e3ce521513584a53aedc5f9765c1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert each component of the normalized floating-point vector into unsigned integer values.  <a href="a00298.html#gad3e7e3ce521513584a53aedc5f9765c1">More...</a><br /></td></tr>
<tr class="separator:gad3e7e3ce521513584a53aedc5f9765c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2b1fd1e854705b1345e98409e0a25e50"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga2b1fd1e854705b1345e98409e0a25e50">unpackF2x11_1x10</a> (uint32 p)</td></tr>
<tr class="memdesc:ga2b1fd1e854705b1345e98409e0a25e50"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into two 11-bit signless floating-point values and one 10-bit signless floating-point value .  <a href="a00298.html#ga2b1fd1e854705b1345e98409e0a25e50">More...</a><br /></td></tr>
<tr class="separator:ga2b1fd1e854705b1345e98409e0a25e50"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab9e60ebe3ad3eeced6a9ec6eb876d74e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab9e60ebe3ad3eeced6a9ec6eb876d74e">unpackF3x9_E1x5</a> (uint32 p)</td></tr>
<tr class="memdesc:gab9e60ebe3ad3eeced6a9ec6eb876d74e"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into two 11-bit signless floating-point values and one 10-bit signless floating-point value .  <a href="a00298.html#gab9e60ebe3ad3eeced6a9ec6eb876d74e">More...</a><br /></td></tr>
<tr class="separator:gab9e60ebe3ad3eeced6a9ec6eb876d74e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga30d6b2f1806315bcd6047131f547d33b"><td class="memTemplParams" colspan="2">template&lt;length_t L, qualifier Q&gt; </td></tr>
<tr class="memitem:ga30d6b2f1806315bcd6047131f547d33b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, float, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#ga30d6b2f1806315bcd6047131f547d33b">unpackHalf</a> (vec&lt; L, uint16, Q &gt; const &amp;p)</td></tr>
<tr class="memdesc:ga30d6b2f1806315bcd6047131f547d33b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a floating-point vector with components obtained by reinterpreting an integer vector as 16-bit floating-point numbers and converting them to 32-bit floating-point values.  <a href="a00298.html#ga30d6b2f1806315bcd6047131f547d33b">More...</a><br /></td></tr>
<tr class="separator:ga30d6b2f1806315bcd6047131f547d33b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac37dedaba24b00adb4ec6e8f92c19dbf"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gac37dedaba24b00adb4ec6e8f92c19dbf">unpackHalf1x16</a> (uint16 v)</td></tr>
<tr class="memdesc:gac37dedaba24b00adb4ec6e8f92c19dbf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a floating-point scalar with components obtained by unpacking a 16-bit unsigned integer into a 16-bit value, interpreted as a 16-bit floating-point number according to the OpenGL Specification, and converting it to 32-bit floating-point values.  <a href="a00298.html#gac37dedaba24b00adb4ec6e8f92c19dbf">More...</a><br /></td></tr>
<tr class="separator:gac37dedaba24b00adb4ec6e8f92c19dbf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga57dfc41b2eb20b0ac00efae7d9c49dcd"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga57dfc41b2eb20b0ac00efae7d9c49dcd">unpackHalf4x16</a> (uint64 p)</td></tr>
<tr class="memdesc:ga57dfc41b2eb20b0ac00efae7d9c49dcd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a four-component floating-point vector with components obtained by unpacking a 64-bit unsigned integer into four 16-bit values, interpreting those values as 16-bit floating-point numbers according to the OpenGL Specification, and converting them to 32-bit floating-point values.  <a href="a00298.html#ga57dfc41b2eb20b0ac00efae7d9c49dcd">More...</a><br /></td></tr>
<tr class="separator:ga57dfc41b2eb20b0ac00efae7d9c49dcd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9a05330e5490be0908d3b117d82aff56"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL ivec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga9a05330e5490be0908d3b117d82aff56">unpackI3x10_1x2</a> (uint32 p)</td></tr>
<tr class="memdesc:ga9a05330e5490be0908d3b117d82aff56"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unpacks a single 32-bit unsigned integer p into three 10-bit and one 2-bit signed integers.  <a href="a00298.html#ga9a05330e5490be0908d3b117d82aff56">More...</a><br /></td></tr>
<tr class="separator:ga9a05330e5490be0908d3b117d82aff56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaccde055882918a3175de82f4ca8b7d8e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL i16vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaccde055882918a3175de82f4ca8b7d8e">unpackInt2x16</a> (int p)</td></tr>
<tr class="memdesc:gaccde055882918a3175de82f4ca8b7d8e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gaccde055882918a3175de82f4ca8b7d8e">More...</a><br /></td></tr>
<tr class="separator:gaccde055882918a3175de82f4ca8b7d8e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab297c0bfd38433524791eb0584d8f08d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL i32vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab297c0bfd38433524791eb0584d8f08d">unpackInt2x32</a> (int64 p)</td></tr>
<tr class="memdesc:gab297c0bfd38433524791eb0584d8f08d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gab297c0bfd38433524791eb0584d8f08d">More...</a><br /></td></tr>
<tr class="separator:gab297c0bfd38433524791eb0584d8f08d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab0c59f1e259fca9e68adb2207a6b665e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL i8vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab0c59f1e259fca9e68adb2207a6b665e">unpackInt2x8</a> (int16 p)</td></tr>
<tr class="memdesc:gab0c59f1e259fca9e68adb2207a6b665e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gab0c59f1e259fca9e68adb2207a6b665e">More...</a><br /></td></tr>
<tr class="separator:gab0c59f1e259fca9e68adb2207a6b665e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga52c154a9b232b62c22517a700cc0c78c"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL i16vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga52c154a9b232b62c22517a700cc0c78c">unpackInt4x16</a> (int64 p)</td></tr>
<tr class="memdesc:ga52c154a9b232b62c22517a700cc0c78c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#ga52c154a9b232b62c22517a700cc0c78c">More...</a><br /></td></tr>
<tr class="separator:ga52c154a9b232b62c22517a700cc0c78c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1cd8d2038cdd33a860801aa155a26221"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL i8vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga1cd8d2038cdd33a860801aa155a26221">unpackInt4x8</a> (int32 p)</td></tr>
<tr class="memdesc:ga1cd8d2038cdd33a860801aa155a26221"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#ga1cd8d2038cdd33a860801aa155a26221">More...</a><br /></td></tr>
<tr class="separator:ga1cd8d2038cdd33a860801aa155a26221"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c1ec97894b05ea21a05aea4f0204a02"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5c1ec97894b05ea21a05aea4f0204a02"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#ga5c1ec97894b05ea21a05aea4f0204a02">unpackRGBM</a> (vec&lt; 4, T, Q &gt; const &amp;rgbm)</td></tr>
<tr class="memdesc:ga5c1ec97894b05ea21a05aea4f0204a02"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a floating-point vector with components obtained by reinterpreting an integer vector as 16-bit floating-point numbers and converting them to 32-bit floating-point values.  <a href="a00298.html#ga5c1ec97894b05ea21a05aea4f0204a02">More...</a><br /></td></tr>
<tr class="separator:ga5c1ec97894b05ea21a05aea4f0204a02"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6d49b31e5c3f9df8e1f99ab62b999482"><td class="memTemplParams" colspan="2">template&lt;typename floatType , length_t L, typename intType , qualifier Q&gt; </td></tr>
<tr class="memitem:ga6d49b31e5c3f9df8e1f99ab62b999482"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, floatType, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#ga6d49b31e5c3f9df8e1f99ab62b999482">unpackSnorm</a> (vec&lt; L, intType, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga6d49b31e5c3f9df8e1f99ab62b999482"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#ga6d49b31e5c3f9df8e1f99ab62b999482">More...</a><br /></td></tr>
<tr class="separator:ga6d49b31e5c3f9df8e1f99ab62b999482"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga96dd15002370627a443c835ab03a766c"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga96dd15002370627a443c835ab03a766c">unpackSnorm1x16</a> (uint16 p)</td></tr>
<tr class="memdesc:ga96dd15002370627a443c835ab03a766c"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 16-bit unsigned integer p into a single 16-bit signed integers.  <a href="a00298.html#ga96dd15002370627a443c835ab03a766c">More...</a><br /></td></tr>
<tr class="separator:ga96dd15002370627a443c835ab03a766c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4851ff86678aa1c7ace9d67846894285"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga4851ff86678aa1c7ace9d67846894285">unpackSnorm1x8</a> (uint8 p)</td></tr>
<tr class="memdesc:ga4851ff86678aa1c7ace9d67846894285"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 8-bit unsigned integer p into a single 8-bit signed integers.  <a href="a00298.html#ga4851ff86678aa1c7ace9d67846894285">More...</a><br /></td></tr>
<tr class="separator:ga4851ff86678aa1c7ace9d67846894285"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b128e89be449fc71336968a66bf6e1a"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga8b128e89be449fc71336968a66bf6e1a">unpackSnorm2x8</a> (uint16 p)</td></tr>
<tr class="memdesc:ga8b128e89be449fc71336968a66bf6e1a"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 16-bit unsigned integer p into a pair of 8-bit signed integers.  <a href="a00298.html#ga8b128e89be449fc71336968a66bf6e1a">More...</a><br /></td></tr>
<tr class="separator:ga8b128e89be449fc71336968a66bf6e1a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7a4fbf79be9740e3c57737bc2af05e5b"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga7a4fbf79be9740e3c57737bc2af05e5b">unpackSnorm3x10_1x2</a> (uint32 p)</td></tr>
<tr class="memdesc:ga7a4fbf79be9740e3c57737bc2af05e5b"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into four 16-bit signed integers.  <a href="a00298.html#ga7a4fbf79be9740e3c57737bc2af05e5b">More...</a><br /></td></tr>
<tr class="separator:ga7a4fbf79be9740e3c57737bc2af05e5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaddf9c353528fe896106f7181219c7f4"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaaddf9c353528fe896106f7181219c7f4">unpackSnorm4x16</a> (uint64 p)</td></tr>
<tr class="memdesc:gaaddf9c353528fe896106f7181219c7f4"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 64-bit unsigned integer p into four 16-bit signed integers.  <a href="a00298.html#gaaddf9c353528fe896106f7181219c7f4">More...</a><br /></td></tr>
<tr class="separator:gaaddf9c353528fe896106f7181219c7f4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga48df3042a7d079767f5891a1bfd8a60a"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uvec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga48df3042a7d079767f5891a1bfd8a60a">unpackU3x10_1x2</a> (uint32 p)</td></tr>
<tr class="memdesc:ga48df3042a7d079767f5891a1bfd8a60a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unpacks a single 32-bit unsigned integer p into three 10-bit and one 2-bit unsigned integers.  <a href="a00298.html#ga48df3042a7d079767f5891a1bfd8a60a">More...</a><br /></td></tr>
<tr class="separator:ga48df3042a7d079767f5891a1bfd8a60a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga035bbbeab7ec2b28c0529757395b645b"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL u16vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga035bbbeab7ec2b28c0529757395b645b">unpackUint2x16</a> (uint p)</td></tr>
<tr class="memdesc:ga035bbbeab7ec2b28c0529757395b645b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#ga035bbbeab7ec2b28c0529757395b645b">More...</a><br /></td></tr>
<tr class="separator:ga035bbbeab7ec2b28c0529757395b645b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf942ff11b65e83eb5f77e68329ebc6ab"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL u32vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaf942ff11b65e83eb5f77e68329ebc6ab">unpackUint2x32</a> (uint64 p)</td></tr>
<tr class="memdesc:gaf942ff11b65e83eb5f77e68329ebc6ab"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gaf942ff11b65e83eb5f77e68329ebc6ab">More...</a><br /></td></tr>
<tr class="separator:gaf942ff11b65e83eb5f77e68329ebc6ab"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa7600a6c71784b637a410869d2a5adcd"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL u8vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaa7600a6c71784b637a410869d2a5adcd">unpackUint2x8</a> (uint16 p)</td></tr>
<tr class="memdesc:gaa7600a6c71784b637a410869d2a5adcd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gaa7600a6c71784b637a410869d2a5adcd">More...</a><br /></td></tr>
<tr class="separator:gaa7600a6c71784b637a410869d2a5adcd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab173834ef14cfc23a96a959f3ff4b8dc"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL u16vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab173834ef14cfc23a96a959f3ff4b8dc">unpackUint4x16</a> (uint64 p)</td></tr>
<tr class="memdesc:gab173834ef14cfc23a96a959f3ff4b8dc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gab173834ef14cfc23a96a959f3ff4b8dc">More...</a><br /></td></tr>
<tr class="separator:gab173834ef14cfc23a96a959f3ff4b8dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf6dc0e4341810a641c7ed08f10e335d1"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL u8vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gaf6dc0e4341810a641c7ed08f10e335d1">unpackUint4x8</a> (uint32 p)</td></tr>
<tr class="memdesc:gaf6dc0e4341810a641c7ed08f10e335d1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer into an integer vector.  <a href="a00298.html#gaf6dc0e4341810a641c7ed08f10e335d1">More...</a><br /></td></tr>
<tr class="separator:gaf6dc0e4341810a641c7ed08f10e335d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e6ac9178b59f0b1b2f7599f2183eb7f"><td class="memTemplParams" colspan="2">template&lt;typename floatType , length_t L, typename uintType , qualifier Q&gt; </td></tr>
<tr class="memitem:ga3e6ac9178b59f0b1b2f7599f2183eb7f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, floatType, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00298.html#ga3e6ac9178b59f0b1b2f7599f2183eb7f">unpackUnorm</a> (vec&lt; L, uintType, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga3e6ac9178b59f0b1b2f7599f2183eb7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#ga3e6ac9178b59f0b1b2f7599f2183eb7f">More...</a><br /></td></tr>
<tr class="separator:ga3e6ac9178b59f0b1b2f7599f2183eb7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga83d34160a5cb7bcb5339823210fc7501"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga83d34160a5cb7bcb5339823210fc7501">unpackUnorm1x16</a> (uint16 p)</td></tr>
<tr class="memdesc:ga83d34160a5cb7bcb5339823210fc7501"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 16-bit unsigned integer p into a of 16-bit unsigned integers.  <a href="a00298.html#ga83d34160a5cb7bcb5339823210fc7501">More...</a><br /></td></tr>
<tr class="separator:ga83d34160a5cb7bcb5339823210fc7501"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab3bc08ecfc0f3339be93fb2b3b56d88a"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gab3bc08ecfc0f3339be93fb2b3b56d88a">unpackUnorm1x5_1x6_1x5</a> (uint16 p)</td></tr>
<tr class="memdesc:gab3bc08ecfc0f3339be93fb2b3b56d88a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#gab3bc08ecfc0f3339be93fb2b3b56d88a">More...</a><br /></td></tr>
<tr class="separator:gab3bc08ecfc0f3339be93fb2b3b56d88a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1319207e30874fb4931a9ee913983ee1"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga1319207e30874fb4931a9ee913983ee1">unpackUnorm1x8</a> (uint8 p)</td></tr>
<tr class="memdesc:ga1319207e30874fb4931a9ee913983ee1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a single 8-bit integer to a normalized floating-point value.  <a href="a00298.html#ga1319207e30874fb4931a9ee913983ee1">More...</a><br /></td></tr>
<tr class="separator:ga1319207e30874fb4931a9ee913983ee1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6abd5a9014df3b5ce4059008d2491260"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga6abd5a9014df3b5ce4059008d2491260">unpackUnorm2x3_1x2</a> (uint8 p)</td></tr>
<tr class="memdesc:ga6abd5a9014df3b5ce4059008d2491260"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#ga6abd5a9014df3b5ce4059008d2491260">More...</a><br /></td></tr>
<tr class="separator:ga6abd5a9014df3b5ce4059008d2491260"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2e50476132fe5f27f08e273d9c70d85b"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga2e50476132fe5f27f08e273d9c70d85b">unpackUnorm2x4</a> (uint8 p)</td></tr>
<tr class="memdesc:ga2e50476132fe5f27f08e273d9c70d85b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#ga2e50476132fe5f27f08e273d9c70d85b">More...</a><br /></td></tr>
<tr class="separator:ga2e50476132fe5f27f08e273d9c70d85b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga637cbe3913dd95c6e7b4c99c61bd611f"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga637cbe3913dd95c6e7b4c99c61bd611f">unpackUnorm2x8</a> (uint16 p)</td></tr>
<tr class="memdesc:ga637cbe3913dd95c6e7b4c99c61bd611f"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 16-bit unsigned integer p into a pair of 8-bit unsigned integers.  <a href="a00298.html#ga637cbe3913dd95c6e7b4c99c61bd611f">More...</a><br /></td></tr>
<tr class="separator:ga637cbe3913dd95c6e7b4c99c61bd611f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5156d3060355fe332865da2c7f78815f"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga5156d3060355fe332865da2c7f78815f">unpackUnorm3x10_1x2</a> (uint32 p)</td></tr>
<tr class="memdesc:ga5156d3060355fe332865da2c7f78815f"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into four 16-bit signed integers.  <a href="a00298.html#ga5156d3060355fe332865da2c7f78815f">More...</a><br /></td></tr>
<tr class="separator:ga5156d3060355fe332865da2c7f78815f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ff95ff5bc16f396432ab67243dbae4d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga5ff95ff5bc16f396432ab67243dbae4d">unpackUnorm3x5_1x1</a> (uint16 p)</td></tr>
<tr class="memdesc:ga5ff95ff5bc16f396432ab67243dbae4d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#ga5ff95ff5bc16f396432ab67243dbae4d">More...</a><br /></td></tr>
<tr class="separator:ga5ff95ff5bc16f396432ab67243dbae4d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2ae149c5d2473ac1e5f347bb654a242d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#ga2ae149c5d2473ac1e5f347bb654a242d">unpackUnorm4x16</a> (uint64 p)</td></tr>
<tr class="memdesc:ga2ae149c5d2473ac1e5f347bb654a242d"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 64-bit unsigned integer p into four 16-bit unsigned integers.  <a href="a00298.html#ga2ae149c5d2473ac1e5f347bb654a242d">More...</a><br /></td></tr>
<tr class="separator:ga2ae149c5d2473ac1e5f347bb654a242d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac58ee89d0e224bb6df5e8bbb18843a2d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00298.html#gac58ee89d0e224bb6df5e8bbb18843a2d">unpackUnorm4x4</a> (uint16 p)</td></tr>
<tr class="memdesc:gac58ee89d0e224bb6df5e8bbb18843a2d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a packed integer to a normalized floating-point vector.  <a href="a00298.html#gac58ee89d0e224bb6df5e8bbb18843a2d">More...</a><br /></td></tr>
<tr class="separator:gac58ee89d0e224bb6df5e8bbb18843a2d"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00119.html" title="GLM_GTC_packing ">glm/gtc/packing.hpp</a>&gt; to use the features of this extension. </p>
<p>This extension provides a set of function to convert vertors to packed formats. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga4944ad465ff950e926d49621f916c78d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint32 glm::packF2x11_1x10 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts the first two components of the normalized floating-point value v into 11-bit signless floating-point values. </p>
<p>Then, converts the third component of the normalized floating-point value v into a 10-bit signless floating-point value. Then, the results are packed into the returned 32-bit unsigned integer.</p>
<p>The first vector component specifies the 11 least-significant bits of the result; the last component specifies the 10 most-significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec3 unpackF2x11_1x10(uint32 const&amp; p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga3f648fc205467792dc6d8c59c748f8a6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint32 glm::packF3x9_E1x5 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts the first two components of the normalized floating-point value v into 11-bit signless floating-point values. </p>
<p>Then, converts the third component of the normalized floating-point value v into a 10-bit signless floating-point value. Then, the results are packed into the returned 32-bit unsigned integer.</p>
<p>The first vector component specifies the 11 least-significant bits of the result; the last component specifies the 10 most-significant bits.</p>
<p>packF3x9_E1x5 allows encoding into RGBE / RGB9E5 format</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec3 unpackF3x9_E1x5(uint32 const&amp; p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga2d8bbce673ebc04831c1fb05c47f5251"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, uint16, Q&gt; glm::packHalf </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, float, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns an unsigned integer vector obtained by converting the components of a floating-point vector to the 16-bit floating-point representation found in the OpenGL Specification. </p>
<p>The first vector component specifies the 16 least-significant bits of the result; the forth component specifies the 16 most-significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec&lt;L, float, Q&gt; unpackHalf(vec&lt;L, uint16, Q&gt; const&amp; p) </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga43f2093b6ff192a79058ff7834fc3528"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint16 glm::packHalf1x16 </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns an unsigned integer obtained by converting the components of a floating-point scalar to the 16-bit floating-point representation found in the OpenGL Specification, and then packing this 16-bit value into a 16-bit unsigned integer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint32 packHalf2x16(vec2 const&amp; v) </dd>
<dd>
uint64 packHalf4x16(vec4 const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packHalf2x16.xml">GLSL packHalf2x16 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafe2f7b39caf8f5ec555e1c059ec530e6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint64 glm::packHalf4x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns an unsigned integer obtained by converting the components of a four-component floating-point vector to the 16-bit floating-point representation found in the OpenGL Specification, and then packing these four 16-bit values into a 64-bit unsigned integer. </p>
<p>The first vector component specifies the 16 least-significant bits of the result; the forth component specifies the 16 most-significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint16 packHalf1x16(float const&amp; v) </dd>
<dd>
uint32 packHalf2x16(vec2 const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packHalf2x16.xml">GLSL packHalf2x16 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga06ecb6afb902dba45419008171db9023"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint32 glm::packI3x10_1x2 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga5abb4603dae0ce58c595e66d9123d812">ivec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns an unsigned integer obtained by converting the components of a four-component signed integer vector to the 10-10-10-2-bit signed integer representation found in the OpenGL Specification, and then packing these four values into a 32-bit unsigned integer. </p>
<p>The first vector component specifies the 10 least-significant bits of the result; the forth component specifies the 2 most-significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint32 packI3x10_1x2(uvec4 const&amp; v) </dd>
<dd>
uint32 packSnorm3x10_1x2(vec4 const&amp; v) </dd>
<dd>
uint32 packUnorm3x10_1x2(vec4 const&amp; v) </dd>
<dd>
ivec4 unpackI3x10_1x2(uint32 const&amp; p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga3644163cf3a47bf1d4af1f4b03013a7e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int glm::packInt2x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2996630ba7b10535af8e065cf326f761">i16vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component from an integer vector into a packed integer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
i16vec2 unpackInt2x16(int p) </dd></dl>

</div>
</div>
<a class="anchor" id="gad1e4c8a9e67d86b61a6eec86703a827a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int64 glm::packInt2x32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga8b44026374982dcd1e52d22bac99247e">i32vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component from an integer vector into a packed integer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
i32vec2 unpackInt2x32(int p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga8884b1f2292414f36d59ef3be5d62914"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int16 glm::packInt2x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#gad06935764d78f43f9d542c784c2212ec">i8vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component from an integer vector into a packed integer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
i8vec2 unpackInt2x8(int16 p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga1989f093a27ae69cf9207145be48b3d7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int64 glm::packInt4x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga550831bfc26d1e0101c1cb3d79938c06">i16vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component from an integer vector into a packed integer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
i16vec4 unpackInt4x16(int64 p) </dd></dl>

</div>
</div>
<a class="anchor" id="gaf2238401d5ce2aaade1a44ba19709072"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int32 glm::packInt4x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga4177a44206121dabc8c4ff1c0f544574">i8vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component from an integer vector into a packed integer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
i8vec4 unpackInt4x8(int32 p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga0466daf4c90f76cc64b3f105ce727295"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, Q&gt; glm::packRGBM </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>rgb</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns an unsigned integer vector obtained by converting the components of a floating-point vector to the 16-bit floating-point representation found in the OpenGL Specification. </p>
<p>The first vector component specifies the 16 least-significant bits of the result; the forth component specifies the 16 most-significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec&lt;3, T, Q&gt; unpackRGBM(vec&lt;4, T, Q&gt; const&amp; p) </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa54b5855a750d6aeb12c1c902f5939b8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, intType, Q&gt; glm::packSnorm </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, floatType, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component of the normalized floating-point vector into signed integer values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec&lt;L, floatType, Q&gt; unpackSnorm(vec&lt;L, intType, Q&gt; const&amp; p); </dd></dl>

</div>
</div>
<a class="anchor" id="gab22f8bcfdb5fc65af4701b25f143c1af"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint16 glm::packSnorm1x16 </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts the normalized floating-point value v into 16-bit integer value. </p>
<p>Then, the results are packed into the returned 16-bit unsigned integer.</p>
<p>The conversion to fixed point is done as follows: packSnorm1x8: round(clamp(s, -1, +1) * 32767.0)</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint32 packSnorm2x16(vec2 const&amp; v) </dd>
<dd>
uint64 packSnorm4x16(vec4 const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packSnorm4x8.xml">GLSL packSnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae3592e0795e62aaa1865b3a10496a7a1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint8 glm::packSnorm1x8 </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>s</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts the normalized floating-point value v into 8-bit integer value. </p>
<p>Then, the results are packed into the returned 8-bit unsigned integer.</p>
<p>The conversion to fixed point is done as follows: packSnorm1x8: round(clamp(s, -1, +1) * 127.0)</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint16 packSnorm2x8(vec2 const&amp; v) </dd>
<dd>
uint32 packSnorm4x8(vec4 const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packSnorm4x8.xml">GLSL packSnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6be3cfb2cce3702f03e91bbeb5286d7e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint16 glm::packSnorm2x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts each component of the normalized floating-point value v into 8-bit integer values. </p>
<p>Then, the results are packed into the returned 16-bit unsigned integer.</p>
<p>The conversion for component c of v to fixed point is done as follows: packSnorm2x8: round(clamp(c, -1, +1) * 127.0)</p>
<p>The first component of the vector will be written to the least significant bits of the output; the last component will be written to the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint8 packSnorm1x8(float const&amp; v) </dd>
<dd>
uint32 packSnorm4x8(vec4 const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packSnorm4x8.xml">GLSL packSnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab997545661877d2c7362a5084d3897d3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint32 glm::packSnorm3x10_1x2 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts the first three components of the normalized floating-point value v into 10-bit signed integer values. </p>
<p>Then, converts the forth component of the normalized floating-point value v into 2-bit signed integer values. Then, the results are packed into the returned 32-bit unsigned integer.</p>
<p>The conversion for component c of v to fixed point is done as follows: packSnorm3x10_1x2(xyz): round(clamp(c, -1, +1) * 511.0) packSnorm3x10_1x2(w): round(clamp(c, -1, +1) * 1.0)</p>
<p>The first vector component specifies the 10 least-significant bits of the result; the forth component specifies the 2 most-significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec4 unpackSnorm3x10_1x2(uint32 const&amp; p) </dd>
<dd>
uint32 packUnorm3x10_1x2(vec4 const&amp; v) </dd>
<dd>
uint32 packU3x10_1x2(uvec4 const&amp; v) </dd>
<dd>
uint32 packI3x10_1x2(ivec4 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="ga358943934d21da947d5bcc88c2ab7832"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint64 glm::packSnorm4x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts each component of the normalized floating-point value v into 16-bit integer values. </p>
<p>Then, the results are packed into the returned 64-bit unsigned integer.</p>
<p>The conversion for component c of v to fixed point is done as follows: packSnorm2x8: round(clamp(c, -1, +1) * 32767.0)</p>
<p>The first component of the vector will be written to the least significant bits of the output; the last component will be written to the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint16 packSnorm1x16(float const&amp; v) </dd>
<dd>
uint32 packSnorm2x16(vec2 const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packSnorm4x8.xml">GLSL packSnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gada3d88d59f0f458f9c51a9fd359a4bc0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint32 glm::packU3x10_1x2 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gaa57e96bb337867329d5f43bcc27c1095">uvec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns an unsigned integer obtained by converting the components of a four-component unsigned integer vector to the 10-10-10-2-bit unsigned integer representation found in the OpenGL Specification, and then packing these four values into a 32-bit unsigned integer. </p>
<p>The first vector component specifies the 10 least-significant bits of the result; the forth component specifies the 2 most-significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint32 packI3x10_1x2(ivec4 const&amp; v) </dd>
<dd>
uint32 packSnorm3x10_1x2(vec4 const&amp; v) </dd>
<dd>
uint32 packUnorm3x10_1x2(vec4 const&amp; v) </dd>
<dd>
ivec4 unpackU3x10_1x2(uint32 const&amp; p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga5eecc9e8cbaf51ac6cf57501e670ee19"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint glm::packUint2x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">u16vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component from an integer vector into a packed unsigned integer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
u16vec2 unpackUint2x16(uint p) </dd></dl>

</div>
</div>
<a class="anchor" id="gaa864081097b86e83d8e4a4d79c382b22"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint64 glm::packUint2x32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">u32vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component from an integer vector into a packed unsigned integer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
u32vec2 unpackUint2x32(int p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga3c3c9fb53ae7823b10fa083909357590"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint16 glm::packUint2x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">u8vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component from an integer vector into a packed unsigned integer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
u8vec2 unpackInt2x8(uint16 p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga2ceb62cca347d8ace42ee90317a3f1f9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint64 glm::packUint4x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga529496d75775fb656a07993ea9af2450">u16vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component from an integer vector into a packed unsigned integer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
u16vec4 unpackUint4x16(uint64 p) </dd></dl>

</div>
</div>
<a class="anchor" id="gaa0fe2f09aeb403cd66c1a062f58861ab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint32 glm::packUint4x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga20779a61de2fd526a17f12fe53ec46b1">u8vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component from an integer vector into a packed unsigned integer. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
u8vec4 unpackUint4x8(uint32 p) </dd></dl>

</div>
</div>
<a class="anchor" id="gaccd3f27e6ba5163eb7aa9bc8ff96251a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, uintType, Q&gt; glm::packUnorm </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, floatType, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component of the normalized floating-point vector into unsigned integer values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec&lt;L, floatType, Q&gt; unpackUnorm(vec&lt;L, intType, Q&gt; const&amp; p); </dd></dl>

</div>
</div>
<a class="anchor" id="ga9f82737bf2a44bedff1d286b76837886"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint16 glm::packUnorm1x16 </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts the normalized floating-point value v into a 16-bit integer value. </p>
<p>Then, the results are packed into the returned 16-bit unsigned integer.</p>
<p>The conversion for component c of v to fixed point is done as follows: packUnorm1x16: round(clamp(c, 0, +1) * 65535.0)</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint16 packSnorm1x16(float const&amp; v) </dd>
<dd>
uint64 packSnorm4x16(vec4 const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packUnorm4x8.xml">GLSL packUnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga768e0337dd6246773f14aa0a421fe9a8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint16 glm::packUnorm1x5_1x6_1x5 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component of the normalized floating-point vector into unsigned integer values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec3 unpackUnorm1x5_1x6_1x5(uint16 p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga4b2fa60df3460403817d28b082ee0736"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint8 glm::packUnorm1x8 </td>
          <td>(</td>
          <td class="paramtype">float&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts the normalized floating-point value v into a 8-bit integer value. </p>
<p>Then, the results are packed into the returned 8-bit unsigned integer.</p>
<p>The conversion for component c of v to fixed point is done as follows: packUnorm1x8: round(clamp(c, 0, +1) * 255.0)</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint16 packUnorm2x8(vec2 const&amp; v) </dd>
<dd>
uint32 packUnorm4x8(vec4 const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packUnorm4x8.xml">GLSL packUnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7f9abdb50f9be1aa1c14912504a0d98d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint8 glm::packUnorm2x3_1x2 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga9c3019b13faf179e4ad3626ea66df334">vec3</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component of the normalized floating-point vector into unsigned integer values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec3 unpackUnorm2x3_1x2(uint8 p) </dd></dl>

</div>
</div>
<a class="anchor" id="gab6bbd5be3b8e6db538ecb33a7844481c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint8 glm::packUnorm2x4 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component of the normalized floating-point vector into unsigned integer values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec2 unpackUnorm2x4(uint8 p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga9a666b1c688ab54100061ed06526de6e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint16 glm::packUnorm2x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts each component of the normalized floating-point value v into 8-bit integer values. </p>
<p>Then, the results are packed into the returned 16-bit unsigned integer.</p>
<p>The conversion for component c of v to fixed point is done as follows: packUnorm2x8: round(clamp(c, 0, +1) * 255.0)</p>
<p>The first component of the vector will be written to the least significant bits of the output; the last component will be written to the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint8 packUnorm1x8(float const&amp; v) </dd>
<dd>
uint32 packUnorm4x8(vec4 const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packUnorm4x8.xml">GLSL packUnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8a1ee625d2707c60530fb3fca2980b19"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint32 glm::packUnorm3x10_1x2 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts the first three components of the normalized floating-point value v into 10-bit unsigned integer values. </p>
<p>Then, converts the forth component of the normalized floating-point value v into 2-bit signed uninteger values. Then, the results are packed into the returned 32-bit unsigned integer.</p>
<p>The conversion for component c of v to fixed point is done as follows: packUnorm3x10_1x2(xyz): round(clamp(c, 0, +1) * 1023.0) packUnorm3x10_1x2(w): round(clamp(c, 0, +1) * 3.0)</p>
<p>The first vector component specifies the 10 least-significant bits of the result; the forth component specifies the 2 most-significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec4 unpackUnorm3x10_1x2(uint32 const&amp; p) </dd>
<dd>
uint32 packUnorm3x10_1x2(vec4 const&amp; v) </dd>
<dd>
uint32 packU3x10_1x2(uvec4 const&amp; v) </dd>
<dd>
uint32 packI3x10_1x2(ivec4 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="gaec4112086d7fb133bea104a7c237de52"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint16 glm::packUnorm3x5_1x1 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component of the normalized floating-point vector into unsigned integer values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec4 unpackUnorm3x5_1x1(uint16 p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga1f63c264e7ab63264e2b2a99fd393897"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint64 glm::packUnorm4x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts each component of the normalized floating-point value v into 16-bit integer values. </p>
<p>Then, the results are packed into the returned 64-bit unsigned integer.</p>
<p>The conversion for component c of v to fixed point is done as follows: packUnorm4x16: round(clamp(c, 0, +1) * 65535.0)</p>
<p>The first component of the vector will be written to the least significant bits of the output; the last component will be written to the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint16 packUnorm1x16(float const&amp; v) </dd>
<dd>
uint32 packUnorm2x16(vec2 const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packUnorm4x8.xml">GLSL packUnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad3e7e3ce521513584a53aedc5f9765c1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint16 glm::packUnorm4x4 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert each component of the normalized floating-point vector into unsigned integer values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec4 unpackUnorm4x4(uint16 p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga2b1fd1e854705b1345e98409e0a25e50"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec3 glm::unpackF2x11_1x10 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 32-bit unsigned integer p into two 11-bit signless floating-point values and one 10-bit signless floating-point value . </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned three-component vector.</p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint32 packF2x11_1x10(vec3 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="gab9e60ebe3ad3eeced6a9ec6eb876d74e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec3 glm::unpackF3x9_E1x5 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 32-bit unsigned integer p into two 11-bit signless floating-point values and one 10-bit signless floating-point value . </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned three-component vector.</p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<p>unpackF3x9_E1x5 allows decoding RGBE / RGB9E5 data</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint32 packF3x9_E1x5(vec3 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="ga30d6b2f1806315bcd6047131f547d33b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, float, Q&gt; glm::unpackHalf </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, <a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a floating-point vector with components obtained by reinterpreting an integer vector as 16-bit floating-point numbers and converting them to 32-bit floating-point values. </p>
<p>The first component of the vector is obtained from the 16 least-significant bits of v; the forth component is obtained from the 16 most-significant bits of v.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec&lt;L, uint16, Q&gt; packHalf(vec&lt;L, float, Q&gt; const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac37dedaba24b00adb4ec6e8f92c19dbf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL float glm::unpackHalf1x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a floating-point scalar with components obtained by unpacking a 16-bit unsigned integer into a 16-bit value, interpreted as a 16-bit floating-point number according to the OpenGL Specification, and converting it to 32-bit floating-point values. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec2 unpackHalf2x16(uint32 const&amp; v) </dd>
<dd>
vec4 unpackHalf4x16(uint64 const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackHalf2x16.xml">GLSL unpackHalf2x16 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga57dfc41b2eb20b0ac00efae7d9c49dcd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec4 glm::unpackHalf4x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a four-component floating-point vector with components obtained by unpacking a 64-bit unsigned integer into four 16-bit values, interpreting those values as 16-bit floating-point numbers according to the OpenGL Specification, and converting them to 32-bit floating-point values. </p>
<p>The first component of the vector is obtained from the 16 least-significant bits of v; the forth component is obtained from the 16 most-significant bits of v.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
float unpackHalf1x16(uint16 const&amp; v) </dd>
<dd>
vec2 unpackHalf2x16(uint32 const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackHalf2x16.xml">GLSL unpackHalf2x16 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9a05330e5490be0908d3b117d82aff56"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL ivec4 glm::unpackI3x10_1x2 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unpacks a single 32-bit unsigned integer p into three 10-bit and one 2-bit signed integers. </p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint32 packU3x10_1x2(uvec4 const&amp; v) </dd>
<dd>
vec4 unpackSnorm3x10_1x2(uint32 const&amp; p); </dd>
<dd>
uvec4 unpackI3x10_1x2(uint32 const&amp; p); </dd></dl>

</div>
</div>
<a class="anchor" id="gaccde055882918a3175de82f4ca8b7d8e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL i16vec2 glm::unpackInt2x16 </td>
          <td>(</td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer into an integer vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
int packInt2x16(i16vec2 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="gab297c0bfd38433524791eb0584d8f08d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL i32vec2 glm::unpackInt2x32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer into an integer vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
int packInt2x16(i32vec2 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="gab0c59f1e259fca9e68adb2207a6b665e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL i8vec2 glm::unpackInt2x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer into an integer vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
int16 packInt2x8(i8vec2 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="ga52c154a9b232b62c22517a700cc0c78c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL i16vec4 glm::unpackInt4x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#gaff5189f97f9e842d9636a0f240001b2e">int64</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer into an integer vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
int64 packInt4x16(i16vec4 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="ga1cd8d2038cdd33a860801aa155a26221"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL i8vec4 glm::unpackInt4x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">int32</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer into an integer vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
int32 packInt2x8(i8vec4 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="ga5c1ec97894b05ea21a05aea4f0204a02"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::unpackRGBM </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>rgbm</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a floating-point vector with components obtained by reinterpreting an integer vector as 16-bit floating-point numbers and converting them to 32-bit floating-point values. </p>
<p>The first component of the vector is obtained from the 16 least-significant bits of v; the forth component is obtained from the 16 most-significant bits of v.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec&lt;4, T, Q&gt; packRGBM(vec&lt;3, float, Q&gt; const&amp; v) </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6d49b31e5c3f9df8e1f99ab62b999482"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, floatType, Q&gt; glm::unpackSnorm </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, intType, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer to a normalized floating-point vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec&lt;L, intType, Q&gt; packSnorm(vec&lt;L, floatType, Q&gt; const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="ga96dd15002370627a443c835ab03a766c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL float glm::unpackSnorm1x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 16-bit unsigned integer p into a single 16-bit signed integers. </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned scalar.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackSnorm1x16: clamp(f / 32767.0, -1, +1)</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec2 unpackSnorm2x16(uint32 p) </dd>
<dd>
vec4 unpackSnorm4x16(uint64 p) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackSnorm1x16.xml">GLSL unpackSnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4851ff86678aa1c7ace9d67846894285"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL float glm::unpackSnorm1x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 8-bit unsigned integer p into a single 8-bit signed integers. </p>
<p>Then, the value is converted to a normalized floating-point value to generate the returned scalar.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackSnorm1x8: clamp(f / 127.0, -1, +1)</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec2 unpackSnorm2x8(uint16 p) </dd>
<dd>
vec4 unpackSnorm4x8(uint32 p) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackSnorm4x8.xml">GLSL unpackSnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8b128e89be449fc71336968a66bf6e1a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec2 glm::unpackSnorm2x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 16-bit unsigned integer p into a pair of 8-bit signed integers. </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned two-component vector.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackSnorm2x8: clamp(f / 127.0, -1, +1)</p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
float unpackSnorm1x8(uint8 p) </dd>
<dd>
vec4 unpackSnorm4x8(uint32 p) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackSnorm4x8.xml">GLSL unpackSnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7a4fbf79be9740e3c57737bc2af05e5b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec4 glm::unpackSnorm3x10_1x2 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 32-bit unsigned integer p into four 16-bit signed integers. </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned four-component vector.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackSnorm3x10_1x2(xyz): clamp(f / 511.0, -1, +1) unpackSnorm3x10_1x2(w): clamp(f / 511.0, -1, +1)</p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint32 packSnorm3x10_1x2(vec4 const&amp; v) </dd>
<dd>
vec4 unpackUnorm3x10_1x2(uint32 const&amp; p)) </dd>
<dd>
uvec4 unpackI3x10_1x2(uint32 const&amp; p) </dd>
<dd>
uvec4 unpackU3x10_1x2(uint32 const&amp; p) </dd></dl>

</div>
</div>
<a class="anchor" id="gaaddf9c353528fe896106f7181219c7f4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec4 glm::unpackSnorm4x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 64-bit unsigned integer p into four 16-bit signed integers. </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned four-component vector.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackSnorm4x16: clamp(f / 32767.0, -1, +1)</p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
float unpackSnorm1x16(uint16 p) </dd>
<dd>
vec2 unpackSnorm2x16(uint32 p) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackSnorm2x16.xml">GLSL unpackSnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga48df3042a7d079767f5891a1bfd8a60a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uvec4 glm::unpackU3x10_1x2 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unpacks a single 32-bit unsigned integer p into three 10-bit and one 2-bit unsigned integers. </p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint32 packU3x10_1x2(uvec4 const&amp; v) </dd>
<dd>
vec4 unpackSnorm3x10_1x2(uint32 const&amp; p); </dd>
<dd>
uvec4 unpackI3x10_1x2(uint32 const&amp; p); </dd></dl>

</div>
</div>
<a class="anchor" id="ga035bbbeab7ec2b28c0529757395b645b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL u16vec2 glm::unpackUint2x16 </td>
          <td>(</td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer into an integer vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint packUint2x16(u16vec2 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="gaf942ff11b65e83eb5f77e68329ebc6ab"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL u32vec2 glm::unpackUint2x32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer into an integer vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
int packUint2x16(u32vec2 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="gaa7600a6c71784b637a410869d2a5adcd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL u8vec2 glm::unpackUint2x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer into an integer vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint16 packInt2x8(u8vec2 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="gab173834ef14cfc23a96a959f3ff4b8dc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL u16vec4 glm::unpackUint4x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer into an integer vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint64 packUint4x16(u16vec4 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="gaf6dc0e4341810a641c7ed08f10e335d1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL u8vec4 glm::unpackUint4x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer into an integer vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint32 packUint4x8(u8vec2 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="ga3e6ac9178b59f0b1b2f7599f2183eb7f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, floatType, Q&gt; glm::unpackUnorm </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, uintType, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer to a normalized floating-point vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec&lt;L, intType, Q&gt; packUnorm(vec&lt;L, floatType, Q&gt; const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="ga83d34160a5cb7bcb5339823210fc7501"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL float glm::unpackUnorm1x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 16-bit unsigned integer p into a of 16-bit unsigned integers. </p>
<p>Then, the value is converted to a normalized floating-point value to generate the returned scalar.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackUnorm1x16: f / 65535.0</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec2 unpackUnorm2x16(uint32 p) </dd>
<dd>
vec4 unpackUnorm4x16(uint64 p) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackUnorm2x16.xml">GLSL unpackUnorm2x16 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab3bc08ecfc0f3339be93fb2b3b56d88a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec3 glm::unpackUnorm1x5_1x6_1x5 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer to a normalized floating-point vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint16 packUnorm1x5_1x6_1x5(vec3 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="ga1319207e30874fb4931a9ee913983ee1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL float glm::unpackUnorm1x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a single 8-bit integer to a normalized floating-point value. </p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackUnorm4x8: f / 255.0</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
vec2 unpackUnorm2x8(uint16 p) </dd>
<dd>
vec4 unpackUnorm4x8(uint32 p) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackUnorm4x8.xml">GLSL unpackUnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6abd5a9014df3b5ce4059008d2491260"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec3 glm::unpackUnorm2x3_1x2 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer to a normalized floating-point vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint8 packUnorm2x3_1x2(vec3 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="ga2e50476132fe5f27f08e273d9c70d85b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec2 glm::unpackUnorm2x4 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer to a normalized floating-point vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint8 packUnorm2x4(vec2 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="ga637cbe3913dd95c6e7b4c99c61bd611f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec2 glm::unpackUnorm2x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 16-bit unsigned integer p into a pair of 8-bit unsigned integers. </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned two-component vector.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackUnorm4x8: f / 255.0</p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
float unpackUnorm1x8(uint8 v) </dd>
<dd>
vec4 unpackUnorm4x8(uint32 p) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackUnorm4x8.xml">GLSL unpackUnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5156d3060355fe332865da2c7f78815f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec4 glm::unpackUnorm3x10_1x2 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 32-bit unsigned integer p into four 16-bit signed integers. </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned four-component vector.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackSnorm3x10_1x2(xyz): clamp(f / 1023.0, 0, +1) unpackSnorm3x10_1x2(w): clamp(f / 3.0, 0, +1)</p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint32 packSnorm3x10_1x2(vec4 const&amp; v) </dd>
<dd>
vec4 unpackInorm3x10_1x2(uint32 const&amp; p)) </dd>
<dd>
uvec4 unpackI3x10_1x2(uint32 const&amp; p) </dd>
<dd>
uvec4 unpackU3x10_1x2(uint32 const&amp; p) </dd></dl>

</div>
</div>
<a class="anchor" id="ga5ff95ff5bc16f396432ab67243dbae4d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec4 glm::unpackUnorm3x5_1x1 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer to a normalized floating-point vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint16 packUnorm3x5_1x1(vec4 const&amp; v) </dd></dl>

</div>
</div>
<a class="anchor" id="ga2ae149c5d2473ac1e5f347bb654a242d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec4 glm::unpackUnorm4x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">uint64</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 64-bit unsigned integer p into four 16-bit unsigned integers. </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned four-component vector.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackUnormx4x16: f / 65535.0</p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
float unpackUnorm1x16(uint16 p) </dd>
<dd>
vec2 unpackUnorm2x16(uint32 p) </dd>
<dd>
<a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackUnorm2x16.xml">GLSL unpackUnorm2x16 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac58ee89d0e224bb6df5e8bbb18843a2d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec4 glm::unpackUnorm4x4 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a packed integer to a normalized floating-point vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00298.html" title="Include <glm/gtc/packing.hpp> to use the features of this extension. ">GLM_GTC_packing</a> </dd>
<dd>
uint16 packUnorm4x4(vec4 const&amp; v) </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
