<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: glm Directory Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">glm Directory Reference</div>  </div>
</div><!--header-->
<div class="contents">
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="subdirs"></a>
Directories</h2></td></tr>
<tr class="memitem:dir_033f5edb0915b828d2c46ed4804e5503"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_033f5edb0915b828d2c46ed4804e5503.html">detail</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:dir_6b66465792d005310484819a0eb0b0d3"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:dir_4c6bd29c73fa4e5a2509e1c15f846751"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:dir_f35778ec600a1b9bbc4524e62e226aa2"><td class="memItemLeft" align="right" valign="top">directory &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="dir_f35778ec600a1b9bbc4524e62e226aa2.html">gtx</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="files"></a>
Files</h2></td></tr>
<tr class="memitem:a00015"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00015.html">common.hpp</a> <a href="a00015_source.html">[code]</a></td></tr>
<tr class="memdesc:a00015"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00026"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00026.html">exponential.hpp</a> <a href="a00026_source.html">[code]</a></td></tr>
<tr class="memdesc:a00026"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00027"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00027.html">ext.hpp</a> <a href="a00027_source.html">[code]</a></td></tr>
<tr class="memdesc:a00027"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> (Dependence) <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00035"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><b>fwd.hpp</b> <a href="a00035_source.html">[code]</a></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00036"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00036.html">geometric.hpp</a> <a href="a00036_source.html">[code]</a></td></tr>
<tr class="memdesc:a00036"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00037"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00037.html">glm.hpp</a> <a href="a00037_source.html">[code]</a></td></tr>
<tr class="memdesc:a00037"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00043"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00043.html">integer.hpp</a> <a href="a00043_source.html">[code]</a></td></tr>
<tr class="memdesc:a00043"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00048"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00048.html">mat2x2.hpp</a> <a href="a00048_source.html">[code]</a></td></tr>
<tr class="memdesc:a00048"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00049"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00049.html">mat2x3.hpp</a> <a href="a00049_source.html">[code]</a></td></tr>
<tr class="memdesc:a00049"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00050"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00050.html">mat2x4.hpp</a> <a href="a00050_source.html">[code]</a></td></tr>
<tr class="memdesc:a00050"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00051"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00051.html">mat3x2.hpp</a> <a href="a00051_source.html">[code]</a></td></tr>
<tr class="memdesc:a00051"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00052"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00052.html">mat3x3.hpp</a> <a href="a00052_source.html">[code]</a></td></tr>
<tr class="memdesc:a00052"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00053"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00053.html">mat3x4.hpp</a> <a href="a00053_source.html">[code]</a></td></tr>
<tr class="memdesc:a00053"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00054"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00054.html">mat4x2.hpp</a> <a href="a00054_source.html">[code]</a></td></tr>
<tr class="memdesc:a00054"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00055"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00055.html">mat4x3.hpp</a> <a href="a00055_source.html">[code]</a></td></tr>
<tr class="memdesc:a00055"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00056"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00056.html">mat4x4.hpp</a> <a href="a00056_source.html">[code]</a></td></tr>
<tr class="memdesc:a00056"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00057"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00057.html">matrix.hpp</a> <a href="a00057_source.html">[code]</a></td></tr>
<tr class="memdesc:a00057"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00120"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00120.html">packing.hpp</a> <a href="a00120_source.html">[code]</a></td></tr>
<tr class="memdesc:a00120"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00160"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00160.html">trigonometric.hpp</a> <a href="a00160_source.html">[code]</a></td></tr>
<tr class="memdesc:a00160"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00184"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00184.html">vec2.hpp</a> <a href="a00184_source.html">[code]</a></td></tr>
<tr class="memdesc:a00184"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00185"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00185.html">vec3.hpp</a> <a href="a00185_source.html">[code]</a></td></tr>
<tr class="memdesc:a00185"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00186"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00186.html">vec4.hpp</a> <a href="a00186_source.html">[code]</a></td></tr>
<tr class="memdesc:a00186"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00225"><td class="memItemLeft" align="right" valign="top">file &#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00225.html">vector_relational.hpp</a> <a href="a00225_source.html">[code]</a></td></tr>
<tr class="memdesc:a00225"><td class="mdescLeft">&#160;</td><td class="mdescRight"><a class="el" href="a00280.html">Core features</a> <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
