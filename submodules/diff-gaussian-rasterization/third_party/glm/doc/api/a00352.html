<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_quaternion</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_quaternion<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00126.html" title="GLM_GTX_quaternion ">glm/gtx/quaternion.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga2f32f970411c44cdd38bb98960198385"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2f32f970411c44cdd38bb98960198385"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#ga2f32f970411c44cdd38bb98960198385">cross</a> (qua&lt; T, Q &gt; const &amp;q, vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga2f32f970411c44cdd38bb98960198385"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute a cross product between a quaternion and a vector.  <a href="a00352.html#ga2f32f970411c44cdd38bb98960198385">More...</a><br /></td></tr>
<tr class="separator:ga2f32f970411c44cdd38bb98960198385"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9f5f77255756e5668dfee7f0d07ed021"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga9f5f77255756e5668dfee7f0d07ed021"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#ga9f5f77255756e5668dfee7f0d07ed021">cross</a> (vec&lt; 3, T, Q &gt; const &amp;v, qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:ga9f5f77255756e5668dfee7f0d07ed021"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute a cross product between a vector and a quaternion.  <a href="a00352.html#ga9f5f77255756e5668dfee7f0d07ed021">More...</a><br /></td></tr>
<tr class="separator:ga9f5f77255756e5668dfee7f0d07ed021"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga321953c1b2e7befe6f5dcfddbfc6b76b"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga321953c1b2e7befe6f5dcfddbfc6b76b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#ga321953c1b2e7befe6f5dcfddbfc6b76b">extractRealComponent</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:ga321953c1b2e7befe6f5dcfddbfc6b76b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extract the real component of a quaternion.  <a href="a00352.html#ga321953c1b2e7befe6f5dcfddbfc6b76b">More...</a><br /></td></tr>
<tr class="separator:ga321953c1b2e7befe6f5dcfddbfc6b76b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga264e10708d58dd0ff53b7902a2bd2561"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga264e10708d58dd0ff53b7902a2bd2561"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#ga264e10708d58dd0ff53b7902a2bd2561">fastMix</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T const &amp;a)</td></tr>
<tr class="memdesc:ga264e10708d58dd0ff53b7902a2bd2561"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quaternion normalized linear interpolation.  <a href="a00352.html#ga264e10708d58dd0ff53b7902a2bd2561">More...</a><br /></td></tr>
<tr class="separator:ga264e10708d58dd0ff53b7902a2bd2561"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacc5cd5f3e78de61d141c2355417424de"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gacc5cd5f3e78de61d141c2355417424de"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#gacc5cd5f3e78de61d141c2355417424de">intermediate</a> (qua&lt; T, Q &gt; const &amp;prev, qua&lt; T, Q &gt; const &amp;curr, qua&lt; T, Q &gt; const &amp;next)</td></tr>
<tr class="memdesc:gacc5cd5f3e78de61d141c2355417424de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an intermediate control point for squad interpolation.  <a href="a00352.html#gacc5cd5f3e78de61d141c2355417424de">More...</a><br /></td></tr>
<tr class="separator:gacc5cd5f3e78de61d141c2355417424de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga58a609b1b8ab965f5df2702e8ca4e75b"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga58a609b1b8ab965f5df2702e8ca4e75b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#ga58a609b1b8ab965f5df2702e8ca4e75b">length2</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:ga58a609b1b8ab965f5df2702e8ca4e75b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the squared length of x.  <a href="a00352.html#ga58a609b1b8ab965f5df2702e8ca4e75b">More...</a><br /></td></tr>
<tr class="separator:ga58a609b1b8ab965f5df2702e8ca4e75b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ee8332600b2aca3a77622a28d857b55"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5ee8332600b2aca3a77622a28d857b55"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#ga5ee8332600b2aca3a77622a28d857b55">quat_identity</a> ()</td></tr>
<tr class="memdesc:ga5ee8332600b2aca3a77622a28d857b55"><td class="mdescLeft">&#160;</td><td class="mdescRight">Create an identity quaternion.  <a href="a00352.html#ga5ee8332600b2aca3a77622a28d857b55">More...</a><br /></td></tr>
<tr class="separator:ga5ee8332600b2aca3a77622a28d857b55"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga07da6ef58646442efe93b0c273d73776"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga07da6ef58646442efe93b0c273d73776"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#ga07da6ef58646442efe93b0c273d73776">rotate</a> (qua&lt; T, Q &gt; const &amp;q, vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga07da6ef58646442efe93b0c273d73776"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns quarternion square root.  <a href="a00352.html#ga07da6ef58646442efe93b0c273d73776">More...</a><br /></td></tr>
<tr class="separator:ga07da6ef58646442efe93b0c273d73776"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafcb78dfff45fbf19a7fcb2bd03fbf196"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gafcb78dfff45fbf19a7fcb2bd03fbf196"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#gafcb78dfff45fbf19a7fcb2bd03fbf196">rotate</a> (qua&lt; T, Q &gt; const &amp;q, vec&lt; 4, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gafcb78dfff45fbf19a7fcb2bd03fbf196"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotates a 4 components vector by a quaternion.  <a href="a00352.html#gafcb78dfff45fbf19a7fcb2bd03fbf196">More...</a><br /></td></tr>
<tr class="separator:gafcb78dfff45fbf19a7fcb2bd03fbf196"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga03e61282831cc3f52cc76f72f52ad2c5"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga03e61282831cc3f52cc76f72f52ad2c5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#ga03e61282831cc3f52cc76f72f52ad2c5">rotation</a> (vec&lt; 3, T, Q &gt; const &amp;orig, vec&lt; 3, T, Q &gt; const &amp;dest)</td></tr>
<tr class="memdesc:ga03e61282831cc3f52cc76f72f52ad2c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute the rotation between two vectors.  <a href="a00352.html#ga03e61282831cc3f52cc76f72f52ad2c5">More...</a><br /></td></tr>
<tr class="separator:ga03e61282831cc3f52cc76f72f52ad2c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadc576cc957adc2a568cdcbc3799175bc"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gadc576cc957adc2a568cdcbc3799175bc"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#gadc576cc957adc2a568cdcbc3799175bc">shortMix</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T const &amp;a)</td></tr>
<tr class="memdesc:gadc576cc957adc2a568cdcbc3799175bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Quaternion interpolation using the rotation short path.  <a href="a00352.html#gadc576cc957adc2a568cdcbc3799175bc">More...</a><br /></td></tr>
<tr class="separator:gadc576cc957adc2a568cdcbc3799175bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0b9bf3459e132ad8a18fe970669e3e35"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0b9bf3459e132ad8a18fe970669e3e35"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#ga0b9bf3459e132ad8a18fe970669e3e35">squad</a> (qua&lt; T, Q &gt; const &amp;q1, qua&lt; T, Q &gt; const &amp;q2, qua&lt; T, Q &gt; const &amp;s1, qua&lt; T, Q &gt; const &amp;s2, T const &amp;h)</td></tr>
<tr class="memdesc:ga0b9bf3459e132ad8a18fe970669e3e35"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute a point on a path according squad equation.  <a href="a00352.html#ga0b9bf3459e132ad8a18fe970669e3e35">More...</a><br /></td></tr>
<tr class="separator:ga0b9bf3459e132ad8a18fe970669e3e35"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaab0afabb894b28a983fb8ec610409d56"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaab0afabb894b28a983fb8ec610409d56"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#gaab0afabb894b28a983fb8ec610409d56">toMat3</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gaab0afabb894b28a983fb8ec610409d56"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a quaternion to a 3 * 3 matrix.  <a href="a00352.html#gaab0afabb894b28a983fb8ec610409d56">More...</a><br /></td></tr>
<tr class="separator:gaab0afabb894b28a983fb8ec610409d56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadfa2c77094e8cc9adad321d938855ffb"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gadfa2c77094e8cc9adad321d938855ffb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#gadfa2c77094e8cc9adad321d938855ffb">toMat4</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gadfa2c77094e8cc9adad321d938855ffb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a quaternion to a 4 * 4 matrix.  <a href="a00352.html#gadfa2c77094e8cc9adad321d938855ffb">More...</a><br /></td></tr>
<tr class="separator:gadfa2c77094e8cc9adad321d938855ffb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga798de5d186499c9a9231cd92c8afaef1"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga798de5d186499c9a9231cd92c8afaef1"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#ga798de5d186499c9a9231cd92c8afaef1">toQuat</a> (mat&lt; 3, 3, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga798de5d186499c9a9231cd92c8afaef1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a 3 * 3 matrix to a quaternion.  <a href="a00352.html#ga798de5d186499c9a9231cd92c8afaef1">More...</a><br /></td></tr>
<tr class="separator:ga798de5d186499c9a9231cd92c8afaef1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5eb36f51e1638e710451eba194dbc011"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5eb36f51e1638e710451eba194dbc011"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00352.html#ga5eb36f51e1638e710451eba194dbc011">toQuat</a> (mat&lt; 4, 4, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga5eb36f51e1638e710451eba194dbc011"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts a 4 * 4 matrix to a quaternion.  <a href="a00352.html#ga5eb36f51e1638e710451eba194dbc011">More...</a><br /></td></tr>
<tr class="separator:ga5eb36f51e1638e710451eba194dbc011"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00126.html" title="GLM_GTX_quaternion ">glm/gtx/quaternion.hpp</a>&gt; to use the features of this extension. </p>
<p>Extented quaternion types and functions </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga2f32f970411c44cdd38bb98960198385"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::cross </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute a cross product between a quaternion and a vector. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9f5f77255756e5668dfee7f0d07ed021"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::cross </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute a cross product between a vector and a quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga321953c1b2e7befe6f5dcfddbfc6b76b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::extractRealComponent </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extract the real component of a quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga264e10708d58dd0ff53b7902a2bd2561"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::fastMix </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>a</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Quaternion normalized linear interpolation. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacc5cd5f3e78de61d141c2355417424de"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::intermediate </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>prev</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>curr</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>next</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns an intermediate control point for squad interpolation. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga58a609b1b8ab965f5df2702e8ca4e75b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::length2 </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the squared length of x. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5ee8332600b2aca3a77622a28d857b55"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::quat_identity </td>
          <td>(</td>
          <td class="paramname"></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Create an identity quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga07da6ef58646442efe93b0c273d73776"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::rotate </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns quarternion square root. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> Rotates a 3 components vector by a quaternion.</dd>
<dd>
<a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafcb78dfff45fbf19a7fcb2bd03fbf196"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, T, Q&gt; glm::rotate </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotates a 4 components vector by a quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga03e61282831cc3f52cc76f72f52ad2c5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::rotation </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>orig</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>dest</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute the rotation between two vectors. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">orig</td><td>vector, needs to be normalized </td></tr>
    <tr><td class="paramname">dest</td><td>vector, needs to be normalized</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gadc576cc957adc2a568cdcbc3799175bc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::shortMix </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>a</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Quaternion interpolation using the rotation short path. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0b9bf3459e132ad8a18fe970669e3e35"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::squad </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>s1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>s2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>h</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute a point on a path according squad equation. </p>
<p>q1 and q2 are control points; s1 and s2 are intermediate control points.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaab0afabb894b28a983fb8ec610409d56"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; glm::toMat3 </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a quaternion to a 3 * 3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

<p>Definition at line <a class="el" href="a00126_source.html#l00113">113</a> of file <a class="el" href="a00126_source.html">gtx/quaternion.hpp</a>.</p>

<p>References <a class="el" href="a00299.html#ga333ab70047fbe4132406100c292dbc89">glm::mat3_cast()</a>.</p>

</div>
</div>
<a class="anchor" id="gadfa2c77094e8cc9adad321d938855ffb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::toMat4 </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a quaternion to a 4 * 4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

<p>Definition at line <a class="el" href="a00126_source.html#l00120">120</a> of file <a class="el" href="a00126_source.html">gtx/quaternion.hpp</a>.</p>

<p>References <a class="el" href="a00299.html#ga1113212d9bdefc2e31ad40e5bbb506f3">glm::mat4_cast()</a>.</p>

</div>
</div>
<a class="anchor" id="ga798de5d186499c9a9231cd92c8afaef1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::toQuat </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a 3 * 3 matrix to a quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

<p>Definition at line <a class="el" href="a00126_source.html#l00127">127</a> of file <a class="el" href="a00126_source.html">gtx/quaternion.hpp</a>.</p>

<p>References <a class="el" href="a00299.html#ga1108a4ab88ca87bac321454eea7702f8">glm::quat_cast()</a>.</p>

</div>
</div>
<a class="anchor" id="ga5eb36f51e1638e710451eba194dbc011"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::toQuat </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts a 4 * 4 matrix to a quaternion. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00352.html" title="Include <glm/gtx/quaternion.hpp> to use the features of this extension. ">GLM_GTX_quaternion</a> </dd></dl>

<p>Definition at line <a class="el" href="a00126_source.html#l00134">134</a> of file <a class="el" href="a00126_source.html">gtx/quaternion.hpp</a>.</p>

<p>References <a class="el" href="a00299.html#ga1108a4ab88ca87bac321454eea7702f8">glm::quat_cast()</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
