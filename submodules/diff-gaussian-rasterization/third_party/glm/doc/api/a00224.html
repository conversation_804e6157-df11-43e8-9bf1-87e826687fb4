<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: vector_relational.hpp File Reference</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_6b66465792d005310484819a0eb0b0d3.html">ext</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">ext/vector_relational.hpp File Reference</div>  </div>
</div><!--header-->
<div class="contents">

<p><a class="el" href="a00275.html">GLM_EXT_vector_relational</a>  
<a href="#details">More...</a></p>

<p><a href="a00224_source.html">Go to the source code of this file.</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga2ac7651a2fa7354f2da610dbd50d28e2"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2ac7651a2fa7354f2da610dbd50d28e2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00275.html#ga2ac7651a2fa7354f2da610dbd50d28e2">equal</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, T epsilon)</td></tr>
<tr class="memdesc:ga2ac7651a2fa7354f2da610dbd50d28e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &lt; epsilon.  <a href="a00275.html#ga2ac7651a2fa7354f2da610dbd50d28e2">More...</a><br /></td></tr>
<tr class="separator:ga2ac7651a2fa7354f2da610dbd50d28e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga37d261a65f69babc82cec2ae1af7145f"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga37d261a65f69babc82cec2ae1af7145f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00275.html#ga37d261a65f69babc82cec2ae1af7145f">equal</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;epsilon)</td></tr>
<tr class="memdesc:ga37d261a65f69babc82cec2ae1af7145f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &lt; epsilon.  <a href="a00275.html#ga37d261a65f69babc82cec2ae1af7145f">More...</a><br /></td></tr>
<tr class="separator:ga37d261a65f69babc82cec2ae1af7145f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2b46cb50911e97b32f4cd743c2c69771"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2b46cb50911e97b32f4cd743c2c69771"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00275.html#ga2b46cb50911e97b32f4cd743c2c69771">equal</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, int ULPs)</td></tr>
<tr class="memdesc:ga2b46cb50911e97b32f4cd743c2c69771"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison between two vectors in term of ULPs.  <a href="a00275.html#ga2b46cb50911e97b32f4cd743c2c69771">More...</a><br /></td></tr>
<tr class="separator:ga2b46cb50911e97b32f4cd743c2c69771"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7da2b8605be7f245b39cb6fbf6d9d581"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga7da2b8605be7f245b39cb6fbf6d9d581"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00275.html#ga7da2b8605be7f245b39cb6fbf6d9d581">equal</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, int, Q &gt; const &amp;ULPs)</td></tr>
<tr class="memdesc:ga7da2b8605be7f245b39cb6fbf6d9d581"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison between two vectors in term of ULPs.  <a href="a00275.html#ga7da2b8605be7f245b39cb6fbf6d9d581">More...</a><br /></td></tr>
<tr class="separator:ga7da2b8605be7f245b39cb6fbf6d9d581"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4a99cc41341567567a608719449c1fac"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4a99cc41341567567a608719449c1fac"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00275.html#ga4a99cc41341567567a608719449c1fac">notEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, T epsilon)</td></tr>
<tr class="memdesc:ga4a99cc41341567567a608719449c1fac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &gt;= epsilon.  <a href="a00275.html#ga4a99cc41341567567a608719449c1fac">More...</a><br /></td></tr>
<tr class="separator:ga4a99cc41341567567a608719449c1fac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga417cf51304359db18e819dda9bce5767"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga417cf51304359db18e819dda9bce5767"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00275.html#ga417cf51304359db18e819dda9bce5767">notEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;epsilon)</td></tr>
<tr class="memdesc:ga417cf51304359db18e819dda9bce5767"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &gt;= epsilon.  <a href="a00275.html#ga417cf51304359db18e819dda9bce5767">More...</a><br /></td></tr>
<tr class="separator:ga417cf51304359db18e819dda9bce5767"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b5c2c3f83422ae5b71fa960d03b0339"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8b5c2c3f83422ae5b71fa960d03b0339"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00275.html#ga8b5c2c3f83422ae5b71fa960d03b0339">notEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, int ULPs)</td></tr>
<tr class="memdesc:ga8b5c2c3f83422ae5b71fa960d03b0339"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison between two vectors in term of ULPs.  <a href="a00275.html#ga8b5c2c3f83422ae5b71fa960d03b0339">More...</a><br /></td></tr>
<tr class="separator:ga8b5c2c3f83422ae5b71fa960d03b0339"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0b15ffe32987a6029b14398eb0def01a"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0b15ffe32987a6029b14398eb0def01a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00275.html#ga0b15ffe32987a6029b14398eb0def01a">notEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, int, Q &gt; const &amp;ULPs)</td></tr>
<tr class="memdesc:ga0b15ffe32987a6029b14398eb0def01a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison between two vectors in term of ULPs.  <a href="a00275.html#ga0b15ffe32987a6029b14398eb0def01a">More...</a><br /></td></tr>
<tr class="separator:ga0b15ffe32987a6029b14398eb0def01a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<div class="textblock"><p><a class="el" href="a00275.html">GLM_EXT_vector_relational</a> </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00280.html" title="Features that implement in C++ the GLSL specification as closely as possible. ">Core features</a> (dependence) </dd>
<dd>
<a class="el" href="a00261.html" title="Include <glm/ext/scalar_integer.hpp> to use the features of this extension. ">GLM_EXT_scalar_integer</a> (dependence) </dd></dl>

<p>Definition in file <a class="el" href="a00224_source.html">ext/vector_relational.hpp</a>.</p>
</div></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
